<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Enhanced Quiz Platform</title>
  <!-- Modern, accessible font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="quiz_style.css">
  <link rel="stylesheet" href="themes.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="stylesheet" href="performance_styles.css">
  <link rel="stylesheet" href="performance/styles/matching_categories.css">
  <link rel="stylesheet" href="performance/styles/tracking.css">
  <!-- Chart.js CDN for performance chart -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!--
    === Developer Reference ===
    Split-View Layout Diagram:
    +-------------------+------------------------------+
    |    Sidebar        |         Main Content         |
    |  (Navigation,     |   (Home, Quiz, Stats, etc.)  |
    |   User, Color     |                              |
    |   Mode Toggle)    |                              |
    +-------------------+------------------------------+
    Implementation Notes:
    - Sidebar is persistent, glassmorphic, with vibrant gradient and accessible contrast.
    - Main content area is scrollable, glassmorphic, and modular.
    - Navigation is via sidebar; top navbar is removed.
    - All interactive elements have ARIA roles and keyboard navigation.
    - See quiz_style.css for glassmorphism, gradients, and microinteraction details.
  -->
</head>
<body class="dark-mode">
  <!-- Accessibility: Skip to main content link -->
  <a href="#mainContent" class="skip-link" tabindex="0">Skip to main content</a>
  <!-- ARIA live region for dynamic feedback -->
  <div id="ariaLive" class="visually-hidden" aria-live="polite" aria-atomic="true"></div>
  <!-- Onboarding Modal -->
  <div id="onboardingModal" class="onboarding-modal hidden" role="dialog" aria-modal="true" aria-labelledby="onboardingTitle">
    <div class="onboarding-content">
      <h2 id="onboardingTitle">Welcome to the Enhanced Quiz Platform!</h2>
      <p>Get started with a quick tour of the new features and navigation.</p>
      <ul>
        <li>Use the sidebar to navigate between Home, Statistics, and Color Mode.</li>
        <li>Try new quiz modes: Flashcards, Drag-and-Drop, and Acronym Learning.</li>
        <li>Earn badges, XP, and streaks for consistent learning!</li>
        <li>Use keyboard shortcuts: <kbd>Alt+H</kbd> for Home, <kbd>Alt+S</kbd> for Stats, <kbd>Alt+N</kbd> for Next.</li>
        <li>Voice input is available in supported quiz modes.</li>
      </ul>
      <button id="closeOnboardingBtn" class="button" aria-label="Close onboarding">Get Started</button>
    </div>
  </div>
  <!-- Contextual Help Modal -->
  <div id="helpModal" class="help-modal hidden" role="dialog" aria-modal="true" aria-labelledby="helpTitle">
    <div class="help-content">
      <h2 id="helpTitle">Need Help?</h2>
      <div id="helpBody">
        <p>Hover over <i class="fa-solid fa-circle-question"></i> icons for tips, or press <kbd>Alt+?</kbd> for this help.</p>
        <ul>
          <li>Navigate with <kbd>Tab</kbd> and <kbd>Shift+Tab</kbd>. All actions are keyboard accessible.</li>
          <li>Use the voice input button <i class="fa-solid fa-microphone"></i> to answer by speaking.</li>
          <li>Badges and XP are earned for correct answers, streaks, and quiz completion.</li>
        </ul>
      </div>
      <button id="closeHelpBtn" class="button" aria-label="Close help">Close</button>
    </div>
  </div>
  <!-- Gamification Bar -->
  <div id="gamificationBar" class="gamification-bar" aria-label="Your Progress and Achievements">
    <span id="xpDisplay" class="xp-display" aria-label="Experience Points">XP: 0</span>
    <span id="streakDisplay" class="streak-display" aria-label="Current Streak">🔥 Streak: 0</span>
    <span id="badgesDisplay" class="badges-display" aria-label="Badges">
      <i class="fa-solid fa-medal" title="No badges yet"></i>
    </span>
    <button id="openHelpBtn" class="help-btn" aria-label="Open Help"><i class="fa-solid fa-circle-question"></i></button>
  </div>
  <!-- Split-view layout: Sidebar + Main Content -->
  <div class="split-view">

    <!-- Main Content Area -->
    <main id="mainContent" class="main-content" tabindex="-1">
      <!-- Sidebar Navigation (moved to top bar) -->
      <nav class="sidebar" aria-label="Main Navigation">
        <div class="sidebar-header">
          <span class="sidebar-logo" aria-hidden="true">
            <i class="fa-solid fa-brain"></i>
          </span>
          <span class="sidebar-title">Quiz Platform</span>
        </div>
        <div class="sidebar-user" id="userInfo" tabindex="0" aria-label="User Info">
          <span>Welcome, <span id="usernameDisplay">Guest</span>!</span>
          <button id="changeNicknameBtn" class="button button-small button-secondary" aria-label="Change Nickname">
            <i class="fa-solid fa-user-pen"></i>
          </button>
        </div>
        <ul class="sidebar-nav" role="menu">
          <li>
            <button id="homeButton" class="sidebar-nav-btn active" role="menuitem" aria-current="page" tabindex="0">
              <i class="fa-solid fa-house"></i> Home
            </button>
          </li>
          <li>
            <button id="statsButton" class="sidebar-nav-btn" role="menuitem" tabindex="0">
              <i class="fa-solid fa-chart-bar"></i> Statistics
            </button>
          </li>
          <li>
            <button id="toggleColorMode" class="sidebar-nav-btn" role="menuitem" tabindex="0">
              <i class="fa-solid fa-circle-half-stroke"></i> <span class="sidebar-color-label">Switch to Light Mode</span>
            </button>
          </li>
        </ul>
      </nav>
      <!-- Nickname Input Section -->
      <div id="userContainer" class="user-container" role="dialog" aria-modal="true" aria-labelledby="nicknameInput">
        <h2>Welcome!</h2>
        <p>Please enter your nickname to continue:</p>
        <input type="text" id="nicknameInput" placeholder="Enter your nickname" aria-label="Nickname">
        <p style="margin-top: 15px;">Enter your Google AI Gemini API Key (Optional, for AI mode):</p>
        <input type="password" id="apiKeyInput" placeholder="Enter your Gemini API Key" aria-label="Gemini API Key">
        <button id="saveNicknameBtn" class="button">Save Nickname & Key</button>
      </div>

  <!-- Home Screen -->
  <div id="homeContainer" class="home-container hidden"> <!-- Removed inline style, added hidden class -->
    <h2>Choose a Quiz Mode</h2>
    <p>Select a quiz mode to test your knowledge in different ways</p>


    <div class="categories-filter">
      <span class="filter-option active" data-category="all">
        <span class="filter-option-text">All Categories</span>
        <div class="filter-option-progress"></div>
      </span>
      <span class="filter-option" data-category="A">
        <span class="filter-option-text">Section A</span>
        <div class="filter-option-progress"></div>
      </span>
      <span class="filter-option" data-category="B">
        <span class="filter-option-text">Section B</span>
        <div class="filter-option-progress"></div>
      </span>
      <span class="filter-option" data-category="C">
        <span class="filter-option-text">Section C</span>
        <div class="filter-option-progress"></div>
      </span>
    </div>

    <div class="difficulty-filter">
      <span class="filter-option active" data-difficulty="all">
        <span class="filter-option-text">All Difficulties</span>
        <div class="filter-option-progress"></div>
      </span>
      <span class="filter-option" data-difficulty="performance">
        <span class="filter-option-text">Performance based</span>
        <div class="filter-option-progress"></div>
      </span>
      <!-- Removed Medium Filter -->
      <span class="filter-option" data-difficulty="hard">
        <span class="filter-option-text">Hard</span>
        <div class="filter-option-progress"></div>
      </span>
      <span class="filter-option" data-difficulty="multiple choice">
        <span class="filter-option-text">Multiple Choice</span>
        <div class="filter-option-progress"></div>
      </span>
    </div>

    <div class="quiz-settings">
      <div class="setting-group">
        <label for="questionCount">Number of Questions:</label>
        <input type="number" id="questionCount" min="1" max="243" value="10" class="question-count-input">
      </div>
    </div>

    <div class="quiz-modes">
      <div class="quiz-mode-card" data-mode="classic">
        <h3>Classic Quiz</h3>
        <p>Test your knowledge with multiple-choice questions at your own pace.</p>
        <div class="category-badge">All Sections</div>
      </div>

      <div class="quiz-mode-card" data-mode="timed">
        <h3>Timed Challenge</h3>
        <p>Race against the clock! Answer as many questions as possible within the time limit.</p>
        <div class="difficulty-badge difficulty-medium">Medium</div>
      </div>

      <div class="quiz-mode-card" data-mode="exam">
        <h3>Exam Simulation</h3>
        <p>Simulate a real exam environment with timed sections and review capability.</p>
        <div class="difficulty-badge difficulty-hard">Hard</div>
      </div>

      <div class="quiz-mode-card" data-mode="flashcards">
        <h3>Flashcards</h3>
        <p>Quick review of concepts with flashcard-style questions and answers.</p>
        <div class="category-badge">All Sections</div>
      </div>

      <div class="quiz-mode-card" data-mode="select">
        <h3>Select Question</h3>
        <p>Choose a specific question to answer from the available list.</p>
        <div class="category-badge">All Sections</div>
      </div>

      <div class="quiz-mode-card" data-mode="acronyms">
        <h3>Acronym Learning</h3>
        <p>Learn and test your knowledge of CompTIA Security+ acronyms.</p>
        <div class="category-badge">Acronyms</div>
      </div>

      <div class="quiz-mode-card" data-mode="ai-learning">
        <h3>Learning with A.I.</h3>
        <p>Generate Cybersecurity & CompTIA questions using AI.</p>
        <div class="category-badge">AI Generated</div>
      </div>

      <!-- Interactive Challenges feature hidden as requested -->
      <!-- <div class="quiz-mode-card" data-mode="interactive">
        <h3>Interactive Challenges</h3>
        <p>Test your knowledge with drag-and-drop and matching exercises.</p>
        <div class="category-badge">Interactive</div>
      </div> -->
    </div>

    <!-- Theme & Focus Tools Buttons -->
    <div class="enhancement-buttons">
      <button id="openThemeBtn" class="enhancement-button">
        <i class="fa-solid fa-palette"></i> Change Theme
      </button>
      <button id="openFocusToolsBtn" class="enhancement-button">
        <i class="fa-solid fa-clock"></i> Focus Tools
      </button>
      <button id="openAchievementsBtn" class="enhancement-button">
        <i class="fa-solid fa-trophy"></i> Achievements
      </button>
    </div>
  </div>

  <!-- Quiz Container -->
  <div id="quizContainer" class="quiz-container hidden">
    <div id="quizHeader">
      <div class="progress-info">
        <span id="questionNumber">Question 1/10</span>
        <span id="timeInfo" class="time-indicator"></span>
      </div>
      <div class="progress-bar">
        <div id="progressBar" class="progress"></div>
      </div>
      <div class="weekly-progress-bar">
        <div id="weeklyProgressBar" class="weekly-progress"></div>
      </div>
      <div class="xp-container">
        <div class="xp-level">1</div>
        <div class="xp-bar-container">
          <div class="xp-bar" style="width: 0%"></div>
        </div>
        <div class="xp-text">0 XP</div>
      </div>
      <div class="streak-container">
        <span class="streak-flame">🔥</span>
        <span class="streak-count">0</span>
        <span class="streak-label">day streak</span>
      </div>
    </div>

    <div id="questionContainer">
      <div class="question-favorites">
        <button id="favoriteBtn" class="favorite-btn">★</button>
      </div>
      <div id="categoryTags">
        <span class="category-badge">Section A</span>
        <span class="difficulty-badge difficulty-medium">Medium</span>
      </div>
      <h2 id="questionText">Loading question...</h2>

      <div id="timerContainer" class="hidden"> <!-- Replaced inline style with hidden class -->
        <div id="timer"></div>
      </div>

      <div id="optionsContainer" class="options">
        <!-- Options will be inserted here -->
      </div>

      <div id="resultContainer"></div>

      <div class="help-hint hidden" id="hintContainer"> <!-- Replaced inline style with hidden class -->
        <button id="hintToggle" class="hint-toggle">Show Hint</button>
        <div id="hintContent" class="hint-content hidden"> <!-- Replaced inline style with hidden class -->
          This hint will help you answer the question correctly.
        </div>
      </div>

      <div class="buttons">
        <button id="checkAnswerBtn" class="button"><i class="fa-solid fa-eye"></i></button> <!-- Replaced text with icon -->
        <button id="nextQuestionBtn" class="button" disabled>Next Question</button>
        <button id="aiFeaturesBtn" class="button hidden" aria-label="AI Features"><i class="fa-solid fa-robot"></i> AI Tools</button> <!-- New Button -->
      </div>
    </div>
  </div>

  <!-- Statistics Container -->
  <div id="statsContainer" class="stats-container hidden">
    <h2>Your Performance Statistics</h2>

    <!-- Overall Stats -->
    <h3>Overall</h3>
    <div class="stats-grid" id="overallStatsGrid">
      <div class="stat-card">
        <h4>Total Questions</h4>
        <div id="overallTotalQuestionsValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Correct Answers</h4>
        <div id="overallCorrectAnswersValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Accuracy</h4>
        <div id="overallAccuracyValue" class="stat-value">0%</div>
      </div>
      <div class="stat-card">
        <h4>Average Time</h4>
        <div id="overallAverageTimeValue" class="stat-value">0s</div>
      </div>
    </div>

    <!-- Section A Stats -->
    <h3>Section A</h3>
    <div class="stats-grid" id="sectionAStatsGrid">
      <div class="stat-card">
        <h4>Total Questions</h4>
        <div id="sectionATotalQuestionsValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Correct Answers</h4>
        <div id="sectionACorrectAnswersValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Accuracy</h4>
        <div id="sectionAAccuracyValue" class="stat-value">0%</div>
      </div>
      <div class="stat-card">
        <h4>Average Time</h4>
        <div id="sectionAAverageTimeValue" class="stat-value">0s</div>
      </div>
    </div>

    <!-- Section B Stats -->
    <h3>Section B</h3>
    <div class="stats-grid" id="sectionBStatsGrid">
      <div class="stat-card">
        <h4>Total Questions</h4>
        <div id="sectionBTotalQuestionsValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Correct Answers</h4>
        <div id="sectionBCorrectAnswersValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Accuracy</h4>
        <div id="sectionBAccuracyValue" class="stat-value">0%</div>
      </div>
      <div class="stat-card">
        <h4>Average Time</h4>
        <div id="sectionBAverageTimeValue" class="stat-value">0s</div>
      </div>
    </div>

    <!-- Section C Stats -->
    <h3>Section C</h3>
    <div class="stats-grid" id="sectionCStatsGrid">
      <div class="stat-card">
        <h4>Total Questions</h4>
        <div id="sectionCTotalQuestionsValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Correct Answers</h4>
        <div id="sectionCCorrectAnswersValue" class="stat-value">0</div>
      </div>
      <div class="stat-card">
        <h4>Accuracy</h4>
        <div id="sectionCAccuracyValue" class="stat-value">0%</div>
      </div>
      <div class="stat-card">
        <h4>Average Time</h4>
        <div id="sectionCAverageTimeValue" class="stat-value">0s</div>
      </div>
    </div>

    <h3>Performance by Category (Chart)</h3>
    <div class="performance-chart-container" style="width:100%;max-width:600px;height:320px;margin:24px auto 32px auto;padding:16px 8px;background:rgba(255,255,255,0.05);border-radius:12px;box-shadow:0 2px 12px rgba(0,0,0,0.08);display:flex;align-items:center;justify-content:center;">
      <canvas id="categoryPerformanceChart" width="560" height="260" style="display:block;max-width:100%;max-height:100%;margin:auto;"></canvas>
    </div>

    <h3>Recent Quizzes (Last 10)</h3>
    <div id="recentQuizzesList" class="recent-quizzes-list">
      <!-- Recent quizzes list items will be populated here -->
      <p>No quiz history yet.</p>
    </div>

    <h3>Favorite Questions</h3>
    <div id="favoriteQuestions">
      <p>You haven't saved any favorite questions yet.</p>
    </div>
  </div>

  <!-- Quiz History Detail Container (Initially Hidden) -->
  <div id="quizHistoryDetailContainer" class="quiz-history-detail-container hidden">
    <button id="closeHistoryDetailBtn" class="button button-secondary close-history-btn">&times; Close</button>
    <h2>Quiz Details</h2>
    <div id="quizHistoryDetailContent">
      <!-- Detailed questions and answers will be populated here -->
    </div>
  </div>

  <!-- Select Question Container -->
  <div id="selectQuestionContainer" class="select-question-container hidden">
    <h2>Select a Question</h2>
    <p>Choose a question from the list below based on the current filters.</p>
    <div id="questionSelectionList" class="question-list">
      <!-- Question list will be populated here -->
    </div>
    <div class="buttons">
        <button id="returnHomeFromSelectBtn" class="button">Back to Home</button>
    </div>
  </div>

  <!-- Acronym Learning Container -->
  <div id="acronymsContainer" class="acronyms-container hidden">
    <h2>Acronym Learning</h2>
    <p>Click the eye icon to reveal the meaning of each acronym.</p> <!-- Updated instructions -->
    <div id="acronymsGrid" class="acronyms-grid">
      <!-- Acronym cards will be dynamically inserted here -->
    </div>
    <div class="buttons">
      <!-- Removed checkAcronymsBtn -->
      <button id="nextAcronymsBtn" class="button">Next Set</button>
      <button id="returnHomeFromAcronymsBtn" class="button">Back to Home</button>
    </div>
  </div>

  <!-- Performance Question Container -->
  <div id="performanceQuestionContainer" class="performance-container hidden">
    <!-- Performance question content will be dynamically inserted here -->
  </div>

  <!-- Performance Tracking Container -->
  <div id="performanceTrackingContainer" class="performance-tracking-container hidden">
    <!-- Performance tracking content will be dynamically inserted here -->
  </div>

      <script src="quiz_script.js" defer></script>
      <script src="gamification.js" defer></script>
      <script src="focus_tools.js" defer></script>
      <script src="interactive_questions.js" defer></script>
      <!-- Performance-based question system scripts -->
      <script src="performance/core/performance-utils.js" defer></script>
      <script src="performance/core/performance-state.js" defer></script>
      <script src="performance/core/performance-core.js" defer></script>
      <script src="performance/ui/performance-animations.js" defer></script>
      <script src="performance/ui/performance-renderer.js" defer></script>
      <script src="performance/ui/performance-ui.js" defer></script>
      <script src="performance/types/matching.js" defer></script>
      <script src="performance/types/matching_categories.js" defer></script>
      <script src="performance/types/ordering.js" defer></script>
      <script src="performance/analytics/performance-tracking.js" defer></script>
      <script src="performance/analytics/performance-metrics.js" defer></script>
      <script src="performance/analytics/performance-tracking-ui.js" defer></script>
      <script src="performance-integration.js" defer></script>
      <!-- test_gamification.js removed as requested - not working properly -->
    </main>
  </div>

  <!-- Achievements Modal -->
  <div id="achievementsModal" class="achievements-modal hidden" role="dialog" aria-modal="true" aria-labelledby="achievementsTitle">
    <div class="achievements-content">
      <h2 id="achievementsTitle">Your Achievements</h2>
      <div id="achievementsList" class="achievements-list">
        <!-- Achievements will be populated here -->
        <p>Complete quizzes to earn achievements!</p>
      </div>
      <button id="closeAchievementsBtn" class="button" aria-label="Close achievements">Close</button>
    </div>
  </div>

  <!-- Focus Tools Modal -->
  <div id="focusToolsModal" class="focus-tools-modal hidden" role="dialog" aria-modal="true" aria-labelledby="focusToolsTitle">
    <div class="focus-tools-content">
      <h2 id="focusToolsTitle">Focus Tools</h2>

      <!-- Pomodoro Timer -->
      <div class="pomodoro-container">
        <h3>Pomodoro Timer</h3>
        <div id="pomodoroTimer" class="pomodoro-timer">25:00</div>
        <div id="pomodoroStatus" class="pomodoro-status">Focus Time</div>
        <div id="pomodoroControls" class="pomodoro-controls">
          <button class="pomodoro-button"><i class="fa-solid fa-play"></i> Start</button>
          <button class="pomodoro-button"><i class="fa-solid fa-rotate"></i> Reset</button>
        </div>
      </div>

      <!-- Focus Mode Toggle -->
      <div class="focus-mode-container">
        <h3>Focus Mode</h3>
        <p>Hide distractions and focus on the current quiz.</p>
        <button id="focusModeToggle" class="button">
          <i class="fa-solid fa-eye-slash"></i> Enter Focus Mode
        </button>
      </div>

      <!-- Ambient Sounds -->
      <div class="ambient-sound-container">
        <h3>Ambient Sounds</h3>
        <p>Play background sounds to help you focus.</p>
        <div id="ambientSoundOptions" class="ambient-sound-options">
          <button class="ambient-sound-option active" data-sound="none">None</button>
          <button class="ambient-sound-option" data-sound="rain">Rainfall</button>
          <button class="ambient-sound-option" data-sound="cafe">Coffee Shop</button>
          <button class="ambient-sound-option" data-sound="nature">Nature</button>
          <button class="ambient-sound-option" data-sound="lofi">Lo-Fi Music</button>
        </div>
        <label for="ambientVolume">Volume:</label>
        <input type="range" id="ambientVolume" class="ambient-sound-volume" min="0" max="100" value="50">
      </div>

      <button id="closeFocusToolsBtn" class="button" aria-label="Close focus tools">Close</button>
    </div>
  </div>

  <!-- Theme Selector Modal -->
  <div id="themeModal" class="theme-modal hidden" role="dialog" aria-modal="true" aria-labelledby="themeTitle">
    <div class="theme-content">
      <h2 id="themeTitle">Choose a Theme</h2>
      <div class="theme-options">
        <div class="theme-option" data-theme="standard">
          <div class="theme-preview standard-preview"></div>
          <span>Standard</span>
        </div>
        <div class="theme-option" data-theme="cyberpunk">
          <div class="theme-preview cyberpunk-preview"></div>
          <span>Cyberpunk</span>
        </div>
        <div class="theme-option" data-theme="nature">
          <div class="theme-preview nature-preview"></div>
          <span>Nature</span>
        </div>
        <div class="theme-option" data-theme="space">
          <div class="theme-preview space-preview"></div>
          <span>Space</span>
        </div>
        <div class="theme-option" data-theme="retro">
          <div class="theme-preview retro-preview"></div>
          <span>Retro</span>
        </div>
      </div>
      <button id="closeThemeBtn" class="button" aria-label="Close theme selector">Close</button>
    </div>
  </div>
</body>
</html>
