:root {
  /* Light Mode Variables */
  --bg-color: #f5f5f5;
  --text-color: #333;
  --container-bg: rgb(235, 228, 228);
  --card-bg: #f0f0f0;
  --card-hover-bg: #10ebe7;
  --card-hover-text: #000;
  --primary-color: #007BFF;
  --primary-hover-color: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-gray: #f8f9fa;
  --medium-gray: #e9ecef;
  --dark-gray: #495057;
  --border-color: #ddd;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --progress-bar-bg: #e0e0e0;
  --hint-bg: #f8f9fa;
  --hint-border: #007bff;
  --hint-text: #495057;
  --link-color: #007BFF;
  --link-hover-color: #0056b3;
  --input-bg: white;
  --input-border: #ccc;
  --input-text: #333;
  --drop-zone-bg: #e9ecef;
  --drop-zone-border: #ced4da;
  /* Option alternating colors for explanation */
  --option-a-bg: #f0f7ff;
  --option-b-bg: #e6f7f0;
  --option-c-bg: #fff5e6;
  --option-d-bg: #f7f0f7;
  --drop-zone-hover-border: #007bff;
  --draggable-bg: #ffffff;
  --draggable-border: #adb5bd;
  --draggable-dragging-bg: #d1ecf1;
  --draggable-dragging-border: #bee5eb;

  /* Dark Mode Variables (overridden by .dark-mode) */
  --dark-bg-color: #1e1e1e;
  --dark-text-color: white;
  --dark-container-bg: #333;
  --dark-card-bg: #444;
  --dark-card-hover-bg: #0056b3; /* Adjusted dark hover */
  --dark-card-hover-text: white;
  --dark-primary-color: #0d6efd;
  --dark-primary-hover-color: #408cfd;
  --dark-success-color: #5ed778;
  --dark-danger-color: #ff6b6b; /* Adjusted dark danger */
  --dark-warning-color: #ffda6b; /* Adjusted dark warning */
  --dark-light-gray: #444;
  --dark-medium-gray: #555;
  --dark-dark-gray: #ccc;
  --dark-border-color: #555;
  --dark-progress-bar-bg: #444;
  --dark-hint-bg: #444;
  --dark-hint-border: #0d6efd;
  --dark-hint-text: #ddd;
  --dark-link-color: #0d6efd;
  --dark-link-hover-color: #408cfd;
  --dark-input-bg: #555;
  --dark-input-border: #666;
  --dark-input-text: white;
  --dark-drop-zone-bg: #495057;
  --dark-drop-zone-border: #6c757d;
  /* Dark mode option alternating colors for explanation */
  --dark-option-a-bg: #2a2a2a;
  --dark-option-b-bg: #1a3450;
  --dark-option-c-bg: #2a2a2a;
  --dark-option-d-bg: #1a3450;
  --dark-drop-zone-hover-border: #408cfd;
  --dark-draggable-bg: #555;
  --dark-draggable-border: #6c757d;
  --dark-draggable-dragging-bg: #033c73;
  --dark-draggable-dragging-border: #022a50;
}
/* Visually hidden utility for ARIA live regions */
.visually-hidden {
  position: absolute !important;
  width: 1px; height: 1px;
  padding: 0; margin: -1px;
  overflow: hidden; clip: rect(0,0,0,0);
  border: 0;
}

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: 0; left: 0;
  background: #10ebe7;
  color: #222;
  padding: 10px 18px;
  z-index: 10000;
  border-radius: 0 0 8px 0;
  font-weight: bold;
  transform: translateY(-120%);
  transition: transform 0.2s;
  outline: none;
}
.skip-link:focus {
  transform: translateY(0);
  outline: 3px solid #007BFF;
}

/* Onboarding Modal */
.onboarding-modal,
.help-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(30,34,40,0.85);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
}
.onboarding-modal.hidden,
.help-modal.hidden {
  display: none;
}
.onboarding-content,
.help-content {
  background: rgba(255,255,255,0.98);
  color: #222;
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(16,235,231,0.18), 0 1.5px 8px 0 rgba(0,0,0,0.12);
  padding: 36px 40px 28px 40px;
  max-width: 420px;
  width: 95vw;
  text-align: left;
  position: relative;
  animation: fadeIn 0.4s;
}
.dark-mode .onboarding-content,
.dark-mode .help-content {
  background: #23272f;
  color: #fff;
}
.onboarding-content h2,
.help-content h2 {
  color: #10ebe7;
  margin-top: 0;
}
.onboarding-content ul,
.help-content ul {
  margin: 1em 0 1.5em 1.2em;
  padding: 0;
  color: #333;
}
.dark-mode .onboarding-content ul,
.dark-mode .help-content ul {
  color: #eee;
}
.onboarding-content button,
.help-content button {
  margin-top: 18px;
  width: 100%;
}

/* Gamification Bar */
.gamification-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1.5em;
  background: linear-gradient(90deg, #10ebe7 0%, #007BFF 100%);
  color: #fff;
  padding: 10px 24px;
  font-size: 1.1em;
  font-weight: 600;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 2px 12px 0 rgba(16,235,231,0.10);
  position: sticky;
  top: 0;
  z-index: 100;
}
.dark-mode .gamification-bar {
  background: linear-gradient(90deg, #23272f 0%, #10ebe7 100%);
  color: #fff;
}
.xp-display, .streak-display, .badges-display {
  margin-right: 1em;
  display: inline-flex;
  align-items: center;
  gap: 0.3em;
}
.badges-display i {
  font-size: 1.3em;
  margin-right: 0.2em;
}
.help-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.3em;
  cursor: pointer;
  margin-left: 0.5em;
  transition: color 0.2s, transform 0.2s;
}
.help-btn:focus, .help-btn:hover {
  color: #ffc107;
  outline: 2px solid #ffc107;
  outline-offset: 2px;
  transform: scale(1.15);
}

/* Focus indicators for all interactive elements */
a:focus, button:focus, input:focus, .sidebar-nav-btn:focus, .quiz-mode-card:focus, .filter-option:focus {
  outline: 3px solid #10ebe7 !important;
  outline-offset: 2px;
  z-index: 10;
}

/* Microinteractions for gamification */
.gamification-bar .xp-display, .gamification-bar .streak-display {
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  border-radius: 8px;
  padding: 2px 10px;
}
.gamification-bar .xp-display.updated, .gamification-bar .streak-display.updated {
  background: #fff;
  color: #007BFF;
  box-shadow: 0 0 8px #10ebe7;
  animation: pulse 0.5s;
}
@keyframes pulse {
  0% { box-shadow: 0 0 0 #10ebe7; }
  50% { box-shadow: 0 0 16px #10ebe7; }
  100% { box-shadow: 0 0 0 #10ebe7; }
}

/* Responsive adjustments for new UI */
@media (max-width: 700px) {
  .onboarding-content, .help-content {
    padding: 18px 6px 18px 6px;
    max-width: 98vw;
  }
  .gamification-bar {
    flex-direction: column;
    align-items: flex-start;
    padding: 10px 8px;
    font-size: 1em;
    border-radius: 0 0 10px 10px;
    gap: 0.7em;
  }
}

/* Kbd styling for keyboard shortcut hints */
kbd {
  background: #eee;
  color: #222;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.95em;
  font-family: 'Roboto Mono', monospace;
  margin: 0 2px;
  border: 1px solid #ccc;
}
.dark-mode kbd {
  background: #23272f;
  color: #10ebe7;
  border: 1px solid #10ebe7;
}

/* Base styling */
body {
  font-family: 'Inter', 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(120deg, #f5f7fa 0%, #c3cfe2 100%);
  color: var(--text-color);
  margin: 0;
  min-height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  animation: fadeIn 0.8s ease-out;
  transition: background 0.3s, color 0.3s;
}
body.dark-mode {
  background: linear-gradient(120deg, #181c24 0%, #23272f 100%) !important;
  color: var(--dark-text-color);
}

/* Split-view layout */
.split-view {
  display: flex;
  min-height: 100vh;
  width: 100vw;
  background: none;
}

/* Sidebar styles */
.sidebar {
  width: 100%;
  height: auto;
  min-width: 0;
  max-width: none;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.85) 0%, rgba(16, 235, 231, 0.7) 100%);
  box-shadow: 0 2px 32px 0 rgba(0,0,0,0.10);
  backdrop-filter: blur(18px) saturate(1.2);
  -webkit-backdrop-filter: blur(18px) saturate(1.2);
  border-right: none;
  border-bottom: 1.5px solid rgba(255,255,255,0.18);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 2rem;
  z-index: 20;
  position: relative;
  transition: background 0.4s;
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 0.7em;
  padding: 1.2rem 1.5rem 1.2rem 0;
  font-size: 1.5em;
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.02em;
  user-select: none;
}

.sidebar-logo {
  font-size: 1.6em;
  color: #fff;
  filter: drop-shadow(0 2px 8px #10ebe7);
}

.sidebar-title {
  font-family: 'Inter', 'Roboto', sans-serif;
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.04em;
}

.sidebar-user {
  display: flex;
  align-items: center;
  gap: 0.7em;
  background: rgba(255,255,255,0.18);
  border-radius: 12px;
  margin: 0 0.75rem;
  padding: 0.7em 1em;
  color: #fff;
  font-size: 1em;
  font-weight: 500;
  box-shadow: 0 2px 8px 0 rgba(16,235,231,0.08);
  outline: none;
}
.sidebar-user:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
}

.sidebar-user #usernameDisplay {
  color: #fff;
  font-weight: 700;
  text-shadow: 0 1px 4px #10ebe7;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0 0.75rem;
  display: flex;
  flex-direction: row;
  gap: 0.5em;
  align-items: center;
}

.sidebar-nav-btn {
  width: auto;
  display: flex;
  align-items: center;
  gap: 0.8em;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 1.08em;
  font-family: inherit;
  font-weight: 600;
  padding: 0.85em 1.2em;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s, outline 0.18s;
  outline: none;
  position: relative;
  z-index: 1;
  margin: 0 0.2em;
}
.sidebar-nav-btn:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
  background: rgba(255,255,255,0.18);
}
.sidebar-nav-btn.active,
.sidebar-nav-btn[aria-current="page"] {
  background: linear-gradient(90deg, #10ebe7 0%, #007BFF 100%);
  color: #222;
  box-shadow: 0 2px 12px 0 rgba(16,235,231,0.12);
}
.sidebar-nav-btn:hover:not(.active) {
  background: rgba(255,255,255,0.12);
  color: #fff;
}
.sidebar-nav-btn i {
  font-size: 1.2em;
  min-width: 1.2em;
  text-shadow: 0 1px 4px #10ebe7;
}
.sidebar-color-label {
  font-weight: 500;
  font-size: 1em;
}

@media (max-width: 900px) {
  .sidebar {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 0.5rem;
    height: auto;
    min-width: 0;
    width: 100vw;
    max-width: 100vw;
    border-bottom: 1.5px solid rgba(255,255,255,0.18);
    border-right: none;
  }
  .sidebar-header,
  .sidebar-title,
  .sidebar-user,
  .sidebar-color-label {
    display: none;
  }
  .sidebar-nav {
    flex-direction: row;
    margin: 0;
    padding: 0;
    gap: 0.2em;
  }
  .sidebar-nav-btn {
    justify-content: center;
    padding: 0.85em 0.5em;
    font-size: 1.2em;
    margin: 0 0.1em;
  }
}

/* Main content area */
.main-content {
  flex: 1 1 0;
  min-width: 0;
  min-height: 100vh;
  background: linear-gradient(120deg, rgba(255,255,255,0.7) 0%, rgba(16,235,231,0.10) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 0 2rem 0;
  position: relative;
  z-index: 0;
  transition: background 0.4s;
  outline: none;
  margin-top: 80px; /* Height of the new top bar */
}
.dark-mode .main-content {
  background: linear-gradient(120deg, rgba(34, 37, 44, 0.98) 0%, rgba(30, 34, 40, 0.98) 100%) !important;
}
.main-content:focus {
  outline: 2px solid #10ebe7;
  outline-offset: 2px;
}

/* Glassmorphism for containers and cards */
.home-container, .quiz-container, .stats-container, .select-question-container, .acronyms-container, .user-container {
  background: rgba(255,255,255,0.55);
  box-shadow: 0 8px 32px 0 rgba(16,235,231,0.10), 0 1.5px 8px 0 rgba(0,0,0,0.08);
  border-radius: 18px;
  border: 1.5px solid rgba(255,255,255,0.18);
  backdrop-filter: blur(18px) saturate(1.2);
  -webkit-backdrop-filter: blur(18px) saturate(1.2);
  color: var(--text-color);
  padding: 32px 36px;
  margin-top: 2.5rem;
  max-width: 900px;
  width: 100%;
  animation: fadeIn 0.5s ease-out;
  transition: background 0.3s, color 0.3s, box-shadow 0.3s;
  text-align: center;
}
.dark-mode .home-container,
.dark-mode .quiz-container,
.dark-mode .stats-container,
.dark-mode .select-question-container,
.dark-mode .acronyms-container,
.dark-mode .user-container {
  background: rgba(34, 37, 44, 0.98) !important;
  color: var(--dark-text-color) !important;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.40), 0 1.5px 8px 0 rgba(0,0,0,0.18);
  border: 1.5px solid rgba(80,80,80,0.28);
}
.dark-mode {
  --bg-color: var(--dark-bg-color);
  --text-color: var(--dark-text-color);
  --container-bg: var(--dark-container-bg);
  --card-bg: var(--dark-card-bg);
  --card-hover-bg: var(--dark-card-hover-bg);
  --card-hover-text: var(--dark-card-hover-text);
  --primary-color: var(--dark-primary-color);
  --primary-hover-color: var(--dark-primary-hover-color);
  --success-color: var(--dark-success-color);
  --danger-color: var(--dark-danger-color);
  --warning-color: var(--dark-warning-color);
  --light-gray: var(--dark-light-gray);
  --medium-gray: var(--dark-medium-gray);
  --dark-gray: var(--dark-dark-gray);
  --border-color: var(--dark-border-color);
  --progress-bar-bg: var(--dark-progress-bar-bg);
  --hint-bg: var(--dark-hint-bg);
  --hint-border: var(--dark-hint-border);
  --hint-text: var(--dark-hint-text);
  --link-color: var(--dark-link-color);
  --link-hover-color: var(--dark-link-hover-color);
  --input-bg: var(--dark-input-bg);
  --input-border: var(--dark-input-border);
  --input-text: var(--dark-input-text);
  --drop-zone-bg: var(--dark-drop-zone-bg);
  --drop-zone-border: var(--dark-drop-zone-border);
  --drop-zone-hover-border: var(--dark-drop-zone-hover-border);
  --draggable-bg: var(--dark-draggable-bg);
  --draggable-border: var(--dark-draggable-border);
  --draggable-dragging-bg: var(--dark-draggable-dragging-bg);
  --draggable-dragging-border: var(--dark-draggable-dragging-border);
}
/* Already replaced by new glassmorphism block above */

/* Acronym Learning Styles */
.acronyms-container {
  padding: 20px;
  background-color: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  width: 800px; /* Match other containers */
  max-width: 90%; /* Match other containers */
  margin-left: auto; /* Center the container */
  margin-right: auto; /* Center the container */
}

.acronyms-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Fixed 3 columns */
  gap: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* --- Acronym Learning Styles (Definition Inside Card) --- */
.acronym-card {
  background-color: var(--dark-card-bg); /* Darker background */
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center; /* Center items */
  justify-content: flex-start; /* Align title and button to top */
  gap: 10px; /* Space between title and button */
  height: 170px; /* Fixed height for the card */
  box-sizing: border-box; /* Include padding in height */
  position: relative; /* Needed for absolute positioning of definition */
  color: var(--dark-text-color);
  padding-bottom: 95px; /* Reserve space at bottom for definition */
}

.acronym-title {
  margin: 0;
  font-size: 1.3em;
  font-weight: bold;
  color: var(--dark-text-color);
  flex-shrink: 0;
}

.reveal-acronym-btn {
  /* Uses .button and .button-small styles */
  padding: 4px 10px;
  line-height: 1;
  background-color: var(--primary-color);
  border: none;
  /* margin-top: auto; Removed - button stays below title */
  margin-bottom: 5px; /* Small space below button */
  flex-shrink: 0; /* Prevent button from shrinking */
}

.reveal-acronym-btn:hover {
  background-color: var(--primary-hover-color);
}

.acronym-definition {
  /* Definition box INSIDE the card */
  font-size: 0.9em;
  color: var(--dark-text-color); /* Light text */
  text-align: left;
  padding: 10px;
  background-color: var(--dark-medium-gray); /* Medium grey background */
  border-radius: 6px;
  width: 90%; /* Adjust width for absolute positioning */
  max-width: 100%;
  box-sizing: border-box;
  /* margin-top: 5px; Removed */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: normal;
  height: 80px; /* Fixed height for definition */
  transition: opacity 0.3s ease-out, transform 0.3s ease-out; /* Updated transition */
  opacity: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: absolute; /* Position absolutely */
  bottom: 10px; /* Position near the bottom */
  left: 5%; /* Center horizontally */
  transform: translateY(0); /* Start position for transition */
  line-height: 1.4; /* Improve readability with better line spacing */
}

/* Improve text visibility in dark mode */
.dark-mode .acronym-definition {
  background-color: var(--dark-card-bg, #444); /* Darker background */
  border: 1px solid var(--dark-border-color, #555); /* Add border for definition */
  color: var(--dark-text-color, white); /* Ensure text is white */
}

.acronym-definition.hidden {
  opacity: 0;
  /* height: 0; Removed */
  /* padding-top: 0; Removed */
  /* padding-bottom: 0; Removed */
  /* margin-top: 0; Removed */
  /* border: none; Removed */
  visibility: hidden; /* Ensure it's fully hidden */
  overflow-y: hidden; /* Hide scrollbar when hidden */
  transform: translateY(10px); /* Slide down slightly when hidden */
}
/* --- End Acronym Learning Styles --- */


.quiz-modes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 20px;
}
.quiz-mode-card {
  background: var(--card-bg);
  padding: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s, background 0.3s, color 0.3s; /* Added background/color transition */
  box-shadow: 0 2px 5px var(--shadow-color);
}

.quiz-mode-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px var(--shadow-color);
  background: var(--card-hover-bg); /* Use variable */
  color: var(--card-hover-text); /* Use variable */
}
.quiz-mode-card h3 {
  margin-top: 0;
  color: var(--primary-color);
}

.quiz-mode-card p {
  font-size: 0.9em;
  color: var(--dark-gray); /* Use variable */
}

/* Enhancement Buttons */
.enhancement-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.enhancement-button {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.3s, transform 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.enhancement-button:hover {
  background: var(--primary-hover-color);
  transform: translateY(-3px);
}

.enhancement-button i {
  font-size: 1.2em;
}

/* Modal Styles */
.achievements-modal,
.focus-tools-modal,
.theme-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
}

.achievements-modal.hidden,
.focus-tools-modal.hidden,
.theme-modal.hidden {
  display: none;
}

.achievements-content,
.focus-tools-content,
.theme-content {
  background: white;
  color: var(--text-color);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 30px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: fadeIn 0.4s;
}

.dark-mode .achievements-content,
.dark-mode .focus-tools-content,
.dark-mode .theme-content {
  background: var(--dark-container-bg);
  color: var(--dark-text-color);
}

.achievements-content h2,
.focus-tools-content h2,
.theme-content h2 {
  margin-top: 0;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 20px;
}

/* Achievement List */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

/* Theme Options */
.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.theme-option:hover {
  transform: scale(1.05);
}

.theme-preview {
  width: 100px;
  height: 60px;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 2px solid transparent;
}

.theme-option.active .theme-preview {
  border-color: var(--primary-color);
}

.standard-preview {
  background: linear-gradient(135deg, #4158D0, #C850C0, #FFCC70);
}

.cyberpunk-preview {
  background: linear-gradient(135deg, #000428, #004e92, #00c8ff);
}

.nature-preview {
  background: linear-gradient(135deg, #134E5E, #71B280, #A8E063);
}

.space-preview {
  background: linear-gradient(135deg, #000000, #434343, #7E57C2);
}

.retro-preview {
  background: linear-gradient(135deg, #FF8008, #FFC837, #FF5722);
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: var(--progress-bar-bg);
  border-radius: 5px;
  margin-bottom: 10px;
  overflow: hidden;
}

.weekly-progress-bar {
  width: 100%;
  height: 6px;
  background: var(--progress-bar-bg);
  border-radius: 3px;
  margin-bottom: 5px;
  overflow: hidden;
}

.weekly-reset-info {
  font-size: 0.75em;
  color: var(--dark-gray);
  text-align: right;
  margin-bottom: 15px;
  opacity: 0.8;
  transition: opacity 0.3s;
  cursor: help;
}

.weekly-reset-info:hover {
  opacity: 1;
}

.dark-mode .weekly-reset-info {
  color: var(--dark-dark-gray);
}

.weekly-progress {
  height: 100%;
  background: linear-gradient(90deg, #FF9800, #FF5722);
  width: 0%;
  transition: width 0.8s ease-in-out, background-color 0.8s ease-in-out;
  position: relative;
  overflow: hidden;
}

/* Weekly progress bar pulse animation */
.weekly-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  animation: progress-pulse 3s infinite;
}

/* XP Container */
.xp-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.xp-level {
  background: var(--primary-color);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
  font-size: 0.9em;
}

.xp-bar-container {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.xp-bar {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.5s ease-out;
}

.xp-text {
  margin-left: 10px;
  font-size: 0.85em;
  color: var(--text-color);
}

.dark-mode .xp-text {
  color: var(--dark-text-color);
}

/* Streak Container */
.streak-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.streak-flame {
  color: #FF9800;
  font-size: 18px;
  margin-right: 8px;
}

.streak-count {
  font-weight: bold;
  font-size: 1.1em;
}

.streak-label {
  margin-left: 5px;
  opacity: 0.8;
  font-size: 0.85em;
}

/* Interactive Questions Styles */
.drag-drop-question {
  margin: 20px 0;
}

.drag-items-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.dark-mode .drag-items-container {
  background: rgba(255, 255, 255, 0.05);
}

.drag-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 10px 15px;
  cursor: grab;
  transition: transform 0.2s, box-shadow 0.2s;
  user-select: none;
}

.dark-mode .drag-item {
  background: var(--dark-card-bg);
  border-color: var(--dark-border-color);
  color: var(--dark-text-color);
}

.drag-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.drag-item.dragging {
  opacity: 0.6;
}

.drag-targets-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.drag-target {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.drag-target-label {
  font-weight: bold;
  margin-bottom: 8px;
}

.drag-drop-zone {
  width: 100%;
  min-height: 80px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  transition: border 0.2s, background 0.2s;
}

.dark-mode .drag-drop-zone {
  border-color: var(--dark-border-color);
}

.drag-drop-zone.drag-over {
  border-color: var(--primary-color);
  background: rgba(0, 123, 255, 0.05);
}

.drag-drop-zone.correct-answer {
  border-color: var(--success-color);
  background: rgba(40, 167, 69, 0.1);
}

.drag-drop-zone.incorrect-answer {
  border-color: var(--danger-color);
  background: rgba(220, 53, 69, 0.1);
}

.correct-answer-label {
  margin-top: 10px;
  color: var(--success-color);
  font-size: 0.9em;
}

/* Matching Questions */
.matching-question {
  margin: 20px 0;
}

.matching-container {
  display: flex;
  justify-content: space-between;
  gap: 40px;
  margin-bottom: 20px;
}

.matching-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.matching-item, .matching-match {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 12px 15px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background 0.2s;
}

.dark-mode .matching-item,
.dark-mode .matching-match {
  background: var(--dark-card-bg);
  border-color: var(--dark-border-color);
  color: var(--dark-text-color);
}

.matching-item:hover, .matching-match:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.matching-item.selected, .matching-match.selected {
  background: rgba(0, 123, 255, 0.1);
  border-color: var(--primary-color);
}

.matching-item.correct, .matching-match.correct {
  background: rgba(40, 167, 69, 0.1);
  border-color: var(--success-color);
}

.matching-item.incorrect, .matching-match.incorrect {
  background: rgba(220, 53, 69, 0.1);
  border-color: var(--danger-color);
}

.matching-lines-container {
  position: relative;
  height: 0;
}

.matching-line {
  position: absolute;
  height: 2px;
  background: var(--primary-color);
  transform-origin: 0 0;
  z-index: 1;
}

.matching-line.correct {
  background: var(--success-color);
}

.matching-line.incorrect {
  background: var(--danger-color);
}

.matching-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.matching-instructions {
  font-size: 0.9em;
  color: var(--dark-gray);
  margin-bottom: 10px;
}

.dark-mode .matching-instructions {
  color: var(--dark-dark-gray);
}

.matching-reset-button {
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background 0.2s;
}

.matching-reset-button:hover {
  background: var(--dark-gray);
}

.progress {
  height: 100%;
  background: var(--primary-color);
  width: 0%;
  border-radius: 5px;
  transition: width 0.5s ease-in-out, background-color 0.5s ease-in-out;
  position: relative;
  overflow: hidden;
}

/* Progress bar reset animation */
.progress-bar-reset {
  animation: progress-reset 0.5s ease-in-out;
}

@keyframes progress-reset {
  0% { opacity: 0.2; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

/* Progress bar pulse animation */
.progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  animation: progress-pulse 2s infinite;
}

@keyframes progress-pulse {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.9em;
  color: var(--dark-gray); /* Use variable */
}

.time-indicator {
  color: var(--warning-color); /* Use variable */
  font-weight: bold;
}

/* --- Daily Progress Bars (Home Screen) --- */
.daily-progress-section {
  margin: 25px 0;
  padding: 15px;
  background-color: var(--card-bg); /* Use card background */
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.daily-progress-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--primary-color);
  text-align: left;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.progress-item label {
  flex-basis: 80px; /* Fixed width for labels */
  text-align: right;
  font-size: 0.9em;
  color: var(--dark-gray);
}

.progress-bar-container {
  flex-grow: 1; /* Take remaining space */
  height: 12px; /* Slightly taller */
  background: var(--progress-bar-bg);
  border-radius: 6px;
  overflow: hidden;
}

/* Re-use .progress class for the inner bar */
.progress-bar-container .progress {
  height: 100%;
  background: var(--success-color); /* Use success color for daily progress */
  width: 0%; /* Initial state */
  border-radius: 6px;
  transition: width 0.5s ease-in-out;
}

.progress-label {
  font-size: 0.9em;
  color: var(--dark-gray);
  min-width: 100px; /* Ensure space for text */
  text-align: left;
}
/* --- End Daily Progress Bars --- */

.options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 10px;
  margin: 20px 0;
}
.options label {
  cursor: pointer;
}
.options input[type="radio"], .options input[type="checkbox"] { /* Updated selector */
  display: none;
}
.options label span {
  display: block;
  padding: 15px;
  background: var(--card-bg); /* Use variable */
  border-radius: 5px;
  transition: transform 0.2s, background 0.2s, color 0.2s;
  /* height: 100%; Removed to allow natural height */
  display: flex;
  align-items: center;
  justify-content: center;
}

.options label span:hover {
  transform: scale(1.02);
  background: var(--card-hover-bg); /* Use variable */
  color: var(--card-hover-text); /* Use variable */
}

.options input[type="radio"]:checked + span,
.options input[type="checkbox"]:checked + span { /* Updated selector */
  background: var(--primary-color); /* Use variable */
  color: white;
  border: 2px solid var(--primary-hover-color); /* Use variable */
  box-shadow: 0 0 10px var(--shadow-color); /* Use variable */
}
.buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
/* Ensure buttons in select container are centered if only one */
.select-question-container .buttons {
  justify-content: center;
}

.button {
  background: linear-gradient(90deg, #10ebe7 0%, #007BFF 100%);
  color: #fff;
  border: none;
  padding: 10px 22px;
  cursor: pointer;
  border-radius: 8px;
  font-size: 1em;
  font-family: inherit;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(16,235,231,0.08);
  transition: background 0.25s, color 0.18s, box-shadow 0.18s, transform 0.18s;
  outline: none;
}
.button:focus {
  outline: 2px solid #10ebe7;
  outline-offset: 2px;
}
.button:hover, .button:focus-visible {
  background: linear-gradient(90deg, #007BFF 0%, #10ebe7 100%);
  color: #fff;
  transform: scale(1.06);
  box-shadow: 0 4px 16px 0 rgba(16,235,231,0.16);
}
.button-small {
  padding: 5px 10px;
  font-size: 0.8em;
}
.button-secondary {
  background: var(--secondary-color);
}
.button-secondary:hover {
  background: color-mix(in srgb, var(--secondary-color) 85%, black); /* Darken secondary color */
}
.button:disabled {
  background: var(--medium-gray); /* Use variable */
  color: var(--dark-gray); /* Use variable */
  cursor: not-allowed;
  transform: none;
}

.button-green {
  background: var(--success-color);
}
.button-green:hover {
  background: color-mix(in srgb, var(--success-color) 85%, black); /* Darken success color */
}
.button-danger {
  background: var(--danger-color);
}
.button-danger:hover {
  background: color-mix(in srgb, var(--danger-color) 85%, black); /* Darken danger color */
}
#toggleExplanation {
  background: linear-gradient(145deg, var(--success-color), color-mix(in srgb, var(--success-color) 85%, black)); /* Use variables */
  color: white;
  border: none;
  padding: 10px 16px;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer;
  border-radius: 8px;
  margin-top: 10px;
  transition: background 0.3s, transform 0.2s, box-shadow 0.3s;
}
#toggleExplanation:hover {
  background: linear-gradient(145deg, color-mix(in srgb, var(--success-color) 85%, black), color-mix(in srgb, var(--success-color) 70%, black)); /* Darken */
  transform: scale(1.05);
  box-shadow: 0 4px 8px var(--shadow-color);
}
#explanationBox {
  margin-top: 10px;
  padding: 15px;
  background-color: var(--light-gray); /* Use variable */
  border: 1px solid var(--border-color); /* Use variable */
  border-radius: 8px;
  text-align: left;
  color: var(--text-color); /* Use variable */
  line-height: 1.5; /* Improve readability with better line spacing */
  white-space: pre-line; /* Preserve line breaks */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Style for the explanation title and main text */
#explanationBox > strong:first-child {
  display: block;
  font-size: 1.1em;
  margin-bottom: 10px;
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 5px;
}

/* Style for the main explanation text (before options) */
#explanationBox .main-explanation {
  padding: 18px 20px;
  background-color: rgba(230, 245, 255, 0.7);
  border-radius: 8px;
  margin: 5px 0 20px 0;
  border-left: 5px solid var(--primary-color);
  display: block;
  position: relative;
  box-shadow: 0 2px 5px rgba(0,0,0,0.08);
  font-size: 1.05em;
  line-height: 1.6;
  color: var(--text-color);
}

#explanationBox .main-explanation::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255,255,255,0.7), rgba(255,255,255,0));
  border-radius: 8px;
  pointer-events: none;
}

/* Dark mode styles for explanation box */
.dark-mode #explanationBox {
  background-color: var(--dark-light-gray);
  border-color: var(--dark-border-color);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.dark-mode #explanationBox > strong:first-child {
  color: var(--dark-primary-color);
  border-bottom-color: var(--dark-primary-color);
}

.dark-mode #explanationBox .main-explanation {
  background-color: rgba(255,255,255,0.05);
  border-left-color: var(--dark-secondary-color, var(--secondary-color));
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  color: var(--dark-text-color);
}

.dark-mode #explanationBox .main-explanation::before {
  background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(255,255,255,0));
}

/* Styles for questions.json format explanations */
#explanationBox .incorrect-answers-header {
  margin: 15px 0 10px 0;
  padding: 12px 15px;
  background-color: rgba(255, 243, 224, 0.8);
  border-radius: 6px;
  font-size: 1.05em;
  border-left: 4px solid var(--warning-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  font-weight: bold;
  color: #333;
}

#explanationBox .more-info-header {
  margin: 15px 0 10px 0;
  padding: 12px 15px;
  background-color: rgba(224, 242, 254, 0.8);
  border-radius: 6px;
  font-size: 1.05em;
  border-left: 4px solid var(--info-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  font-weight: bold;
  color: #333;
}

.dark-mode #explanationBox .incorrect-answers-header {
  background-color: rgba(255, 193, 7, 0.15);
  border-left-color: var(--dark-warning-color, var(--warning-color));
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
  color: #eee;
}

.dark-mode #explanationBox .more-info-header {
  background-color: rgba(23, 162, 184, 0.15);
  border-left-color: var(--dark-info-color, var(--info-color));
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
  color: #eee;
}

/* Style for links in explanation box */
#explanationBox a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px dotted var(--primary-color);
  transition: all 0.2s ease;
}

#explanationBox a:hover {
  color: var(--secondary-color);
  border-bottom: 1px solid var(--secondary-color);
}

.dark-mode #explanationBox a {
  color: var(--dark-primary-color);
  border-bottom: 1px dotted var(--dark-primary-color);
}

.dark-mode #explanationBox a:hover {
  color: var(--dark-secondary-color, var(--secondary-color));
  border-bottom: 1px solid var(--dark-secondary-color, var(--secondary-color));
}

/* Style for more information content */
#explanationBox .more-info-content {
  padding: 12px 15px;
  background-color: rgba(224, 242, 254, 0.5);
  border-radius: 6px;
  margin: 5px 0 10px 0;
  border-left: 4px solid var(--info-color);
  font-size: 0.95em;
  line-height: 1.5;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.dark-mode #explanationBox .more-info-content {
  background-color: rgba(23, 162, 184, 0.1);
  border-left-color: var(--dark-info-color, var(--info-color));
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
  color: #eee;
}

/* Option styles for explanation */
#explanationBox .option-a {
  display: block;
  padding: 10px 15px 10px 40px;
  margin: 8px 0;
  background-color: var(--option-a-bg);
  border-radius: 6px;
  border-left: 4px solid var(--primary-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
}

#explanationBox .option-a::before {
  content: "A";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--primary-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

#explanationBox .option-b {
  display: block;
  padding: 10px 15px 10px 40px;
  margin: 8px 0;
  background-color: var(--option-b-bg);
  border-radius: 6px;
  border-left: 4px solid var(--info-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
}

#explanationBox .option-b::before {
  content: "B";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--info-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

#explanationBox .option-c {
  display: block;
  padding: 10px 15px 10px 40px;
  margin: 8px 0;
  background-color: var(--option-c-bg);
  border-radius: 6px;
  border-left: 4px solid var(--warning-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
}

#explanationBox .option-c::before {
  content: "C";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--warning-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

#explanationBox .option-d {
  display: block;
  padding: 10px 15px 10px 40px;
  margin: 8px 0;
  background-color: var(--option-d-bg);
  border-radius: 6px;
  border-left: 4px solid var(--success-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
}

#explanationBox .option-d::before {
  content: "D";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--success-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Dark mode option styles */
.dark-mode #explanationBox .option-a {
  background-color: var(--dark-option-a-bg);
  border-left: 4px solid var(--dark-primary-color);
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
}

.dark-mode #explanationBox .option-b {
  background-color: var(--dark-option-b-bg);
  border-left: 4px solid var(--info-color);
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
}

.dark-mode #explanationBox .option-c {
  background-color: var(--dark-option-c-bg);
  border-left: 4px solid var(--dark-warning-color);
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
}

.dark-mode #explanationBox .option-d {
  background-color: var(--dark-option-d-bg);
  border-left: 4px solid var(--dark-success-color);
  box-shadow: 0 1px 5px rgba(0,0,0,0.3);
}

.report {
  text-align: left;
  margin: 20px 0;
}
.feedback {
  font-weight: bold;
  color: var(--success-color); /* Use variable */
  margin: 15px 0;
  font-size: 1.2em;
}

.categories-filter, .difficulty-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 15px 0;
  justify-content: center;
}
.filter-option {
  padding: 8px 15px;
  background: var(--card-bg); /* Use variable */
  border-radius: 20px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  font-size: 0.9em;
}

.filter-option.active {
  background: var(--primary-color); /* Use variable */
  color: white;
}


/* --- Section Progress Bar Styles (Home Screen Filters) --- */
.filter-option {
  /* Existing styles... */
  position: relative; /* Needed for absolute positioning of progress */
  overflow: hidden; /* Keep progress bar contained */
  z-index: 0; /* Base stacking context */
}

.filter-option-text {
  position: relative; /* To allow z-index */
  z-index: 2; /* Ensure text is above the progress bar */
  color: inherit; /* Inherit color from .filter-option */
}

.filter-option-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0%; /* Initial width */
  background-color: rgba(40, 167, 69, 0.6); /* Semi-transparent success color */
  border-radius: 20px; /* Match parent */
  transition: width 0.5s ease-in-out;
  z-index: 1; /* Behind the text */
}

/* Adjust text color when active/hovering to ensure visibility over progress */
.filter-option.active .filter-option-text,
.filter-option:hover .filter-option-text {
   color: white; /* Ensure text is white when background changes */
}

/* Dark mode adjustment for progress bar */
.dark-mode .filter-option-progress {
    background-color: rgba(94, 215, 120, 0.5); /* Lighter semi-transparent green for dark mode */
}
/* --- End Section Progress Bar Styles --- */

.quiz-settings {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  margin: 20px 0;
}
.quiz-settings select {
  padding: 8px 12px;
  border-radius: 5px;
  border: 1px solid var(--input-border); /* Use variable */
  background-color: var(--input-bg); /* Use variable */
  color: var(--input-text); /* Use variable */
  font-size: 1em;
}

.question-favorites {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.favorite-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5em;
  color: var(--medium-gray); /* Use variable */
  transition: color 0.3s;
}
.favorite-btn.active {
  color: var(--warning-color); /* Use variable */
}
.category-badge {
  display: inline-block;
  padding: 3px 8px;
  background: var(--medium-gray); /* Use variable */
  border-radius: 12px;
  font-size: 0.8em;
  margin-right: 5px;
  color: var(--dark-gray); /* Use variable */
}

.difficulty-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  margin-right: 5px;
  color: white;
}
.difficulty-easy {
  background: var(--success-color);
}
.difficulty-medium {
  background: var(--warning-color); /* Use warning color for medium */
}
.difficulty-hard {
  background: var(--danger-color);
}
.difficulty-multiple-choice {
   background: #6f42c1; /* Using a distinct purple color */
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
}
.stat-card {
  background: var(--light-gray); /* Use variable */
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  color: var(--primary-color); /* Use variable */
  margin: 10px 0;
}

.performance-chart-container {
  height: 250px;
  margin: 20px 0;
}
/* Remove old navbar styles (now handled by sidebar) */
.nav-buttons {
  display: flex;
  gap: 10px;
}
/* Remove old tab-button styles (now handled by sidebar-nav-btn) */

.leaderboard-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}
.leaderboard-table th, .leaderboard-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color); /* Use variable */
}

.leaderboard-table th {
  background-color: var(--light-gray); /* Use variable */
  font-weight: bold;
  color: var(--dark-gray); /* Use variable */
}

.leaderboard-table tr:hover {
  background-color: var(--medium-gray); /* Use variable */
}

.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}
.help-hint {
  background: var(--hint-bg); /* Use variable */
  border-left: 4px solid var(--hint-border); /* Use variable */
  padding: 12px 15px;
  margin: 15px 0;
  border-radius: 0 5px 5px 0;
  font-size: 0.9em;
  color: var(--hint-text); /* Use variable */
  text-align: left;
}

.hint-toggle {
  color: var(--link-color); /* Use variable */
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  font-size: 0.9em;
  text-decoration: underline;
}

.hint-content {
  margin-top: 8px;
}
.feedback-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--input-bg); /* Use variable */
  color: var(--input-text); /* Use variable */
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 0 15px var(--shadow-color); /* Use variable */
  animation: slideIn 0.3s ease-out;
  max-width: 300px;
  z-index: 1000;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
#timer {
  font-size: 1.2em;
  color: var(--danger-color); /* Use variable */
  font-weight: bold;
  margin-top: 10px;
}
.confetti {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  pointer-events: none;
}
.explanation-link {
  color: var(--link-color); /* Use variable */
  text-decoration: none;
  transition: color 0.2s;
}

.explanation-link:hover {
  color: var(--link-hover-color); /* Use variable */
  text-decoration: underline;
}


.setting-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 15px 0;
}

.question-count-input {
  padding: 8px 12px;
  border-radius: 5px;
  border: 1px solid var(--input-border); /* Use variable */
  background-color: var(--input-bg); /* Use variable */
  color: var(--input-text); /* Use variable */
  width: 80px;
  font-size: 1em;
}


/* Added .hidden class */
.hidden {
  display: none;
}

/* Style for the subtle explanation button */
.show-explanation-btn {
  background: none;
  border: none;
  color: var(--link-color);
  cursor: pointer;
  padding: 0; /* Remove padding */
  margin-top: 5px; /* Add some space above */
  font-size: 0.9em; /* Slightly smaller font */
  text-decoration: none; /* No underline by default */
  transition: color 0.2s;
}

.show-explanation-btn:hover {
  color: var(--link-hover-color);
  text-decoration: underline; /* Underline on hover */
}

/* Nickname Input Section Styles */
/* Already handled by glassmorphism block above */

.user-container h2 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.user-container p {
  margin-bottom: 20px;
}

#nicknameInput {
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: center;
  margin: 5px; /* Added margin */
  flex: 1 1 150px; /* Allow wrapping */
  min-width: 120px; /* Prevent becoming too small */
}

/* --- Quiz History --- */
.recent-quizzes-list {
  margin-top: 15px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px;
  background-color: var(--secondary-bg-color);
}

.recent-quiz-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.recent-quiz-item:last-child {
  border-bottom: none;
}

.recent-quiz-item:hover {
  background-color: var(--hover-bg-color);
}

.recent-quiz-item span {
  flex: 1;
  padding: 0 5px;
  text-align: left;
}

.recent-quiz-item .quiz-date { flex-basis: 30%; }
.recent-quiz-item .quiz-mode { flex-basis: 20%; }
.recent-quiz-item .quiz-score { flex-basis: 30%; text-align: center; }
.recent-quiz-item .quiz-time { flex-basis: 20%; text-align: right; }


.quiz-history-detail-container {
  padding: 20px;
  margin-top: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--primary-bg-color);
  position: relative; /* For positioning the close button */
}

.close-history-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 1.5em;
  line-height: 1;
  padding: 5px 10px;
}

.quiz-history-summary {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.quiz-history-summary p {
  margin: 5px 0;
}

/* Re-use question-result styling from results screen */
#quizHistoryDetailContent .question-result {
  margin: 15px 0;
  padding: 15px;
  border-radius: 8px;
  border-left-width: 4px;
  border-left-style: solid;
}

#quizHistoryDetailContent .question-result p {
  margin: 8px 0;
}

#quizHistoryDetailContent .explanation-content {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(0,0,0,0.05); /* Slightly different background */
  border-radius: 4px;
  font-size: 0.9em;
  border-left: 3px solid var(--accent-color);
}

/* Dark mode adjustments for history */
body.dark-mode .recent-quizzes-list {
  background-color: var(--secondary-bg-color);
  border-color: var(--border-color);
}
body.dark-mode .recent-quiz-item {
  border-bottom-color: var(--border-color);
}
body.dark-mode .recent-quiz-item:hover {
  background-color: var(--hover-bg-color);
}
body.dark-mode .quiz-history-detail-container {
  background-color: var(--primary-bg-color);
  border-color: var(--border-color);
}
body.dark-mode #quizHistoryDetailContent .explanation-content {
  background-color: rgba(255,255,255,0.08);
  border-left-color: var(--accent-color);
}

#saveNicknameBtn {
  /* Uses the general .button styles */
  vertical-align: middle; /* Align with input */
}

/* User Info in Navbar */
.user-info {
  display: flex; /* Align items horizontally */
  align-items: center; /* Center items vertically */
  gap: 10px; /* Add space between text and button */
  color: var(--text-color);
  font-size: 0.9em;
  margin-right: auto; /* Pushes nav buttons to the right */
  padding-left: 20px; /* Add some space from the title */
}

#usernameDisplay {
  font-weight: bold;
  color: var(--primary-color);
}

/* Styles for Select Question Mode */
.select-question-container h2 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.select-question-container p {
  margin-bottom: 20px;
  color: var(--dark-gray);
}

.question-list {
  max-height: 400px; /* Limit height and allow scrolling */
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 20px;
  background-color: var(--bg-color); /* Match body background */
}

.question-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 8px;
  transition: background-color 0.2s;
}

.question-list-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.question-list-item:hover {
  background-color: var(--medium-gray);
}

.question-snippet {
  flex-grow: 1;
  margin-right: 15px;
  font-size: 0.95em;
  text-align: left;
}

.select-question-btn {
  /* Uses .button and .button-small styles */
  flex-shrink: 0; /* Prevent button from shrinking */
}

/* --- Matching Question Styles --- */

/* Remove grid layout for matching questions specifically */
.options.matching-options {
  display: block; /* Override grid */
}

.matching-dnd-container {
  display: flex;
  flex-direction: column; /* Stack controls list and locations */
  gap: 20px;
}

.controls-list-container {
  border: 1px dashed var(--border-color);
  padding: 15px;
  border-radius: 8px;
  min-height: 60px; /* Ensure it's visible even when empty */
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-content: flex-start; /* Align items to the top */
  background-color: var(--light-gray);
}
.controls-list-container.over {
  border-color: var(--primary-color);
  background-color: var(--medium-gray);
}

.controls-list-container h4 {
  width: 100%;
  margin: 0 0 10px 0;
  text-align: left;
  color: var(--secondary-color);
  font-size: 0.9em;
}

.draggable-control {
  padding: 8px 12px;
  background-color: var(--draggable-bg);
  border: 1px solid var(--draggable-border);
  border-radius: 4px;
  cursor: grab;
  transition: background-color 0.2s, border-color 0.2s, opacity 0.2s;
  user-select: none; /* Prevent text selection while dragging */
  touch-action: none; /* Improve touch dragging */
}

.draggable-control:active {
  cursor: grabbing;
}

.draggable-control.dragging {
  opacity: 0.5;
  background-color: var(--draggable-dragging-bg);
  border-color: var(--draggable-dragging-border);
}

.draggable-control.hidden {
  /* Keep the element in the layout but invisible,
     so drop zones don't collapse unexpectedly if item is returned */
  visibility: hidden;
  height: 0;
  padding: 0;
  border: none;
  margin: 0;
  opacity: 0;
}

.locations-container {
  display: flex;
  flex-direction: column;
  gap: 15px; /* Space between location rows */
}

.matching-location {
  display: flex;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.matching-location-info {
  flex-grow: 1; /* Take available space */
  flex-basis: 50%; /* Start at 50% */
  text-align: left;
  padding-right: 15px; /* Space between info and drop zones */
  display: flex;
  align-items: center;
}

.matching-location-info strong {
  color: var(--primary-color);
  margin-right: 5px;
}

.matching-location-info span {
  font-size: 0.9em;
  color: var(--dark-gray);
}

.matching-icon {
  margin-right: 8px;
  color: var(--secondary-color);
  font-size: 1.2em; /* Make icon slightly larger */
  width: 20px; /* Fixed width for alignment */
  text-align: center;
}

.drop-zones {
  flex-basis: 45%; /* Adjust as needed */
  display: flex;
  gap: 10px; /* Space between drop zones for a single location */
  justify-content: flex-end; /* Align zones to the right */
}

.drop-zone {
  border: 2px dashed var(--drop-zone-border);
  background-color: var(--drop-zone-bg);
  min-height: 40px; /* Ensure drop zone is visible */
  min-width: 150px; /* Give some width */
  padding: 5px;
  border-radius: 4px;
  transition: border-color 0.2s, background-color 0.2s;
  display: flex; /* Allow dropped item to sit inside */
  align-items: center;
  justify-content: center;
  position: relative; /* For absolute positioning of children if needed */
  z-index: 1; /* Establish stacking context */
}

.drop-zone.over {
  border-color: var(--drop-zone-hover-border);
  background-color: var(--medium-gray);
}

.drop-zone.filled {
  border-style: solid;
}

.drop-zone.filled .draggable-control {
  cursor: default; /* No grab cursor when in drop zone */
  visibility: visible; /* Ensure it's visible */
  height: auto;
  padding: 8px 12px;
  border: 1px solid var(--draggable-border);
  margin: 0;
  opacity: 1;
  width: calc(100% - 10px); /* Take up most of the drop zone width */
  box-sizing: border-box; /* Include padding in width calculation */
  z-index: 2; /* Ensure it's above the drop zone */
}

/* Feedback styles for drop zones after checking */
.drop-zone.correct {
  border-color: var(--success-color);
  border-style: solid;
  background-color: rgba(40, 167, 69, 0.1);
}
.drop-zone.incorrect {
  border-color: var(--danger-color);
  border-style: solid;
  background-color: rgba(220, 53, 69, 0.1);
}
.drop-zone.correct .draggable-control,
.drop-zone.incorrect .draggable-control {
   border-color: transparent; /* Optional: remove border of item inside */
}


/* Responsive adjustments */
@media (max-width: 700px) {
  .main-content {
    padding: 0 0.5rem 2rem 0.5rem;
  }
  .home-container, .quiz-container, .stats-container, .select-question-container, .acronyms-container, .user-container {
    padding: 18px 6px;
    margin-top: 1.2rem;
    max-width: 100vw;
  }
  .matching-location {
    flex-direction: column;
    align-items: flex-start;
  }
  .matching-location-info {
    flex-basis: 100%;
    padding-right: 0;
    margin-bottom: 10px;
  }
  .drop-zones {
    flex-basis: 100%;
    justify-content: flex-start; /* Align left on small screens */
    width: 100%;
  }
  .drop-zone {
     min-width: 100px; /* Adjust width */
     flex-grow: 1; /* Allow zones to take space */
  }
  .matching-dnd-container {
      flex-direction: column; /* Stack controls and locations */
   }
}

/* Styling for location-specific explanations (added dynamically) */
.location-explanation {
  margin-top: 10px; /* Space above the explanation */
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.03); /* Very subtle background */
  border-left: 3px solid var(--info-color); /* Use info color for the border */
  border-radius: 0 4px 4px 0; /* Rounded corners on the right */
  font-size: 0.9em;
  color: var(--text-color); /* Inherit text color */
  text-align: left;
  width: 100%; /* Ensure it takes full width within its container */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Dark mode adjustment for location explanation */
.dark-mode .location-explanation {
  background-color: rgba(255, 255, 255, 0.05); /* Subtle background in dark mode */
  border-left-color: var(--dark-info-color, var(--info-color)); /* Use dark info or fallback */
  color: var(--dark-text-color);
}

/* Rule to hide AI button specifically */
.ai-button-hidden {
  display: none !important;
}

/* AI Tools Result Container Styling */
#aiResultContainer, .ai-result-container {
  background-color: var(--card-bg, #f9f9f9);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 5px;
  padding: 10px;
  margin-top: 15px;
  max-height: 400px;
  overflow-y: auto;
  color: var(--text-color, #333); /* Ensure default text is dark */
}

/* Ensure text is visible in light mode */
#aiResultContainer h3,
.ai-result-container h3,
#aiResultContainer p,
#aiResultContainer div,
.ai-result-container p,
.ai-result-container div {
  color: var(--text-color, #333); /* Dark text in light mode */
}

/* Dark mode styling for AI results */
.dark-mode #aiResultContainer,
.dark-mode .ai-result-container {
  background-color: var(--dark-card-bg, #444);
  border-color: var(--dark-border-color, #555);
  color: var(--dark-text-color, white);
}

/* Ensure text in AI results is visible in dark mode */
.dark-mode #aiResultContainer h3,
.dark-mode .ai-result-container h3 {
  color: var(--dark-primary-color, #0d6efd);
  border-top-color: var(--dark-border-color, #555);
}

.dark-mode #aiResultContainer p,
.dark-mode #aiResultContainer div,
.dark-mode .ai-result-container p,
.dark-mode .ai-result-container div {
  color: var(--dark-text-color, white);
}

.dark-mode #aiResultContainer strong,
.dark-mode .ai-result-container strong {
  color: var(--dark-warning-color, #ffda6b);
}

/* Explanation text styling for acronyms */
.dark-mode .acronyms-container h3,
.dark-mode .acronyms-container h4 {
  color: var(--dark-primary-color, #0d6efd);
}

.dark-mode .acronyms-container p {
  color: var(--dark-text-color, white);
}

/* Specific styling for meaning and explanation headers */
.dark-mode .acronyms-container .meaning-header,
.dark-mode .acronyms-container .explanation-header,
.dark-mode #aiResultContainer strong,
.dark-mode .ai-result-container strong {
  color: var(--dark-warning-color, #ffda6b);
  font-weight: bold;
}

/* Improved formatting for acronym explanations */
.acronym-definition {
  white-space: pre-line; /* Preserve line breaks */
  font-family: 'Inter', 'Roboto', sans-serif; /* Match main font */
}

.acronym-definition strong,
.acronym-definition b {
  color: var(--primary-color); /* Make bold text stand out */
  font-weight: 600;
}

.dark-mode .acronym-definition strong,
.dark-mode .acronym-definition b {
  color: var(--dark-warning-color, #ffda6b); /* Highlight in dark mode */
}

.acronym-definition p {
  margin: 0.5em 0; /* Add spacing between paragraphs */
}

/* Ensure all text in the explanation container is visible */
.dark-mode .acronyms-container,
.dark-mode #aiResultContainer,
.dark-mode .ai-result-container {
  color: var(--dark-text-color, white);
}

/* Improve visibility of explanation text in AI results */
.dark-mode #aiResultContainer p,
.dark-mode #aiResultContainer div,
.dark-mode .ai-result-container p,
.dark-mode .ai-result-container div {
  color: var(--dark-text-color, white);
  line-height: 1.5;
}

/* Make acronym headers stand out in AI results */
.dark-mode #aiResultContainer h3,
.dark-mode .ai-result-container h3 {
  color: var(--dark-primary-color, #0d6efd);
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--dark-border-color, #555);
}

/* Acronyms view button layout */
.acronyms-container .buttons {
  justify-content: center; /* Center buttons */
  flex-wrap: wrap; /* Allow wrapping */
  gap: 10px; /* Add space between buttons */
}

/* AI Tools Button Styling */
.ai-tools-btn {
  background: linear-gradient(90deg, #10ebe7 0%, #007BFF 100%);
  color: #fff;
  border: none;
  padding: 10px 22px;
  cursor: pointer;
  border-radius: 8px;
  font-size: 1em;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(16,235,231,0.08);
  transition: background 0.25s, transform 0.18s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-tools-btn:hover {
  background: linear-gradient(90deg, #007BFF 0%, #10ebe7 100%);
  transform: scale(1.06);
}

.ai-tools-btn i {
  font-size: 1.2em;
}

/* Dark mode AI Tools button */
.dark-mode .ai-tools-btn,
.dark-mode #aiFeaturesBtn {
  background: linear-gradient(90deg, #23272f 0%, #10ebe7 100%);
  color: #fff;
  border: 1px solid var(--dark-border-color, #555);
}

.dark-mode .ai-tools-btn:hover,
.dark-mode #aiFeaturesBtn:hover {
  background: linear-gradient(90deg, #10ebe7 0%, #23272f 100%);
  box-shadow: 0 0 10px rgba(16, 235, 231, 0.3);
}

/* Acronym explanation container styling */
.acronym-definition {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 15px;
  margin: 10px 0;
  transition: all 0.3s ease;
}

/* Dark mode specific styles */
.dark-mode .acronym-definition {
  background-color: var(--dark-card-bg, #444);
  border-color: var(--dark-border-color, #555);
  color: var(--dark-text-color, white);
}

/* AI Result Container Styling */
#aiResultContainer,
.ai-result-container {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 15px;
  margin-top: 15px;
  max-height: 600px; /* Increased from 400px to show more content */
  overflow-y: auto;
  line-height: 1.5; /* Improve readability */
  text-align: left; /* Ensure text is left-aligned */
  word-wrap: break-word; /* Ensure long words don't overflow */
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Dark mode AI results container */
.dark-mode #aiResultContainer,
.dark-mode .ai-result-container {
  background-color: var(--dark-card-bg, #444) !important;
  border-color: var(--dark-border-color, #555);
  color: var(--dark-text-color, white);
}

/* Ensure all text elements are visible in both modes */
#aiResultContainer *,
.ai-result-container * {
  color: inherit;
}

/* Wrapper for explanation content to ensure proper display */
.explanation-content-wrapper {
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
  white-space: normal;
  margin-bottom: 10px;
}

/* Styling for acronym explanations in AI results */
.acronym-explanation {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.03);
  border-left: 3px solid var(--info-color);
  border-radius: 0 4px 4px 0;
  line-height: 1.5;
}

.dark-mode .acronym-explanation {
  background-color: rgba(255, 255, 255, 0.05);
  border-left-color: var(--dark-info-color, var(--info-color));
}

/* Special text styling for headers and labels */
.dark-mode .meaning-header,
.dark-mode .explanation-header,
.dark-mode .original-text,
.dark-mode .romanian-text {
  color: var(--dark-warning-color, #ffda6b);
  font-weight: bold;
}
