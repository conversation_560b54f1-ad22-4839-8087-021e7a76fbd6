document.addEventListener('DOMContentLoaded', function() {
  // Accessibility: Skip link focus management
  const skipLink = document.querySelector('.skip-link');
  if (skipLink) {
    skipLink.addEventListener('click', function(e) {
      e.preventDefault();
      const main = document.getElementById('mainContent');
      if (main) main.focus();
    });
  }

  // Initialize theme
  initializeTheme();

  // Initialize focus tools
  if (window.focusTools) {
    window.focusTools.initPomodoro();
    window.focusTools.initAmbientSounds();
  }

  // Add event listeners for enhancement buttons
  const openThemeBtn = document.getElementById('openThemeBtn');
  const closeThemeBtn = document.getElementById('closeThemeBtn');
  const themeModal = document.getElementById('themeModal');
  const themeOptions = document.querySelectorAll('.theme-option');

  const openFocusToolsBtn = document.getElementById('openFocusToolsBtn');
  const closeFocusToolsBtn = document.getElementById('closeFocusToolsBtn');
  const focusToolsModal = document.getElementById('focusToolsModal');
  const focusModeToggle = document.getElementById('focusModeToggle');
  const ambientSoundOptions = document.querySelectorAll('.ambient-sound-option');
  const ambientVolume = document.getElementById('ambientVolume');

  const openAchievementsBtn = document.getElementById('openAchievementsBtn');
  const closeAchievementsBtn = document.getElementById('closeAchievementsBtn');
  const achievementsModal = document.getElementById('achievementsModal');

  // Theme modal
  if (openThemeBtn && themeModal) {
    openThemeBtn.addEventListener('click', () => {
      themeModal.classList.remove('hidden');
    });
  }

  if (closeThemeBtn && themeModal) {
    closeThemeBtn.addEventListener('click', () => {
      themeModal.classList.add('hidden');
    });
  }

  if (themeOptions) {
    themeOptions.forEach(option => {
      option.addEventListener('click', () => {
        const theme = option.dataset.theme;
        setTheme(theme);

        // Update active class
        themeOptions.forEach(opt => opt.classList.remove('active'));
        option.classList.add('active');
      });
    });
  }

  // Focus tools modal
  if (openFocusToolsBtn && focusToolsModal) {
    openFocusToolsBtn.addEventListener('click', () => {
      focusToolsModal.classList.remove('hidden');
    });
  }

  if (closeFocusToolsBtn && focusToolsModal) {
    closeFocusToolsBtn.addEventListener('click', () => {
      focusToolsModal.classList.add('hidden');
    });
  }

  if (focusModeToggle && window.focusTools) {
    focusModeToggle.addEventListener('click', () => {
      window.focusTools.toggleFocusMode();
    });
  }

  if (ambientSoundOptions && window.focusTools) {
    ambientSoundOptions.forEach(option => {
      option.addEventListener('click', () => {
        const sound = option.dataset.sound;
        const volume = ambientVolume ? ambientVolume.value / 100 : 0.5;

        // Stop current sound if clicking on active option
        if (option.classList.contains('active') && sound !== 'none') {
          window.focusTools.stopAmbientSound();
          ambientSoundOptions.forEach(opt => opt.classList.remove('active'));
          document.querySelector('.ambient-sound-option[data-sound="none"]').classList.add('active');
          return;
        }

        // Play new sound
        window.focusTools.playAmbientSound(sound, volume);

        // Update active class
        ambientSoundOptions.forEach(opt => opt.classList.remove('active'));
        option.classList.add('active');
      });
    });
  }

  if (ambientVolume && window.focusTools) {
    ambientVolume.addEventListener('input', () => {
      const activeSound = document.querySelector('.ambient-sound-option.active');
      if (activeSound && activeSound.dataset.sound !== 'none') {
        const sound = activeSound.dataset.sound;
        const volume = ambientVolume.value / 100;
        window.focusTools.playAmbientSound(sound, volume);
      }
    });
  }

  // Achievements modal
  if (openAchievementsBtn && achievementsModal) {
    openAchievementsBtn.addEventListener('click', () => {
      updateAchievementsList();
      achievementsModal.classList.remove('hidden');
    });
  }

  if (closeAchievementsBtn && achievementsModal) {
    closeAchievementsBtn.addEventListener('click', () => {
      achievementsModal.classList.add('hidden');
    });
  }

  // Global event delegation for AI buttons
  document.body.addEventListener('click', function(e) {
    const aiButton = e.target.closest('#aiFeaturesBtn');
    if (aiButton) {
      console.log('AI button clicked via global delegation');
      e.preventDefault();
      e.stopPropagation();
      handleAiFeaturesClick();
      return false;
    }
  });

  // Onboarding Modal Logic
  const onboardingModal = document.getElementById('onboardingModal');
  const closeOnboardingBtn = document.getElementById('closeOnboardingBtn');
  if (onboardingModal && closeOnboardingBtn) {
    if (!localStorage.getItem('quizOnboardingComplete')) {
      onboardingModal.classList.remove('hidden');
      setTimeout(() => closeOnboardingBtn.focus(), 300);
    }
    closeOnboardingBtn.addEventListener('click', () => {
      onboardingModal.classList.add('hidden');
      localStorage.setItem('quizOnboardingComplete', '1');
      // Focus main content for accessibility
      const main = document.getElementById('mainContent');
      if (main) main.focus();
    });
  }

  // Help Modal Logic
  const helpModal = document.getElementById('helpModal');
  const openHelpBtn = document.getElementById('openHelpBtn');
  const closeHelpBtn = document.getElementById('closeHelpBtn');
  function openHelp() {
    if (helpModal) {
      helpModal.classList.remove('hidden');
      setTimeout(() => closeHelpBtn && closeHelpBtn.focus(), 200);
    }
  }
  function closeHelp() {
    if (helpModal) {
      helpModal.classList.add('hidden');
      openHelpBtn && openHelpBtn.focus();
    }
  }
  if (openHelpBtn) openHelpBtn.addEventListener('click', openHelp);
  if (closeHelpBtn) closeHelpBtn.addEventListener('click', closeHelp);

  // Keyboard shortcuts: Alt+H (Home), Alt+S (Stats), Alt+N (Next), Alt+? (Help)
  document.addEventListener('keydown', function(e) {
    if (e.altKey && !e.shiftKey && !e.ctrlKey) {
      if (e.key.toLowerCase() === 'h') {
        e.preventDefault();
        if (homeButton) homeButton.click();
      } else if (e.key.toLowerCase() === 's') {
        e.preventDefault();
        if (statsButton) statsButton.click();
      } else if (e.key.toLowerCase() === 'n') {
        e.preventDefault();
        if (nextQuestionBtnElement && !nextQuestionBtnElement.disabled) nextQuestionBtnElement.click();
      } else if (e.key === '?') {
        e.preventDefault();
        openHelp();
      }
    }
    // Escape closes modals
    if (e.key === 'Escape') {
      if (onboardingModal && !onboardingModal.classList.contains('hidden')) onboardingModal.classList.add('hidden');
      if (helpModal && !helpModal.classList.contains('hidden')) helpModal.classList.add('hidden');
    }
  });

  // ARIA live region update utility
  window.updateAriaLive = function(message) {
    const ariaLive = document.getElementById('ariaLive');
    if (ariaLive) {
      ariaLive.textContent = '';
      setTimeout(() => { ariaLive.textContent = message; }, 10);
    }
  };

  // --- Theme Functions ---
  function initializeTheme() {
    const savedTheme = localStorage.getItem('quizAppTheme') || 'standard';
    setTheme(savedTheme);

    // Set active class on the current theme option
    const themeOption = document.querySelector(`.theme-option[data-theme="${savedTheme}"]`);
    if (themeOption) {
      themeOption.classList.add('active');
    }
  }

  function setTheme(theme) {
    // Remove all theme classes
    document.body.classList.remove('theme-standard', 'theme-cyberpunk', 'theme-nature', 'theme-space', 'theme-retro');

    // Add the selected theme class
    document.body.classList.add(`theme-${theme}`);

    // Save the theme preference
    localStorage.setItem('quizAppTheme', theme);
  }

  // --- Achievement Functions ---
  function updateAchievementsList() {
    const achievementsList = document.getElementById('achievementsList');
    if (!achievementsList || !window.gamification) return;

    // Clear the list
    achievementsList.innerHTML = '';

    if (!userData || !userData.gamification) {
      achievementsList.innerHTML = '<p>Complete quizzes to earn achievements!</p>';
      return;
    }

    // Get all achievements and user's unlocked achievements
    const allAchievements = window.gamification.getAllAchievements();
    const unlockedAchievements = window.gamification.getUnlockedAchievements(userData);

    if (unlockedAchievements.length === 0) {
      achievementsList.innerHTML = '<p>Complete quizzes to earn achievements!</p>';
      return;
    }

    // Add unlocked achievements
    unlockedAchievements.forEach(achievement => {
      const achievementDate = userData.gamification.achievements[achievement.id]?.dateUnlocked;
      const formattedDate = achievementDate ? new Date(achievementDate).toLocaleDateString() : '';

      const achievementCard = document.createElement('div');
      achievementCard.className = 'achievement-card';
      achievementCard.innerHTML = `
        <div class="achievement-icon">
          <i class="${achievement.icon}"></i>
        </div>
        <div class="achievement-info">
          <div class="achievement-name">${achievement.name}</div>
          <div class="achievement-description">${achievement.description}</div>
          <div class="achievement-date">Unlocked: ${formattedDate}</div>
        </div>
      `;

      achievementsList.appendChild(achievementCard);
    });

    // Add locked achievements
    const lockedAchievements = allAchievements.filter(achievement =>
      !unlockedAchievements.some(unlocked => unlocked.id === achievement.id)
    );

    if (lockedAchievements.length > 0) {
      const lockedHeader = document.createElement('h3');
      lockedHeader.textContent = 'Locked Achievements';
      achievementsList.appendChild(lockedHeader);

      lockedAchievements.forEach(achievement => {
        const achievementCard = document.createElement('div');
        achievementCard.className = 'achievement-card achievement-locked';
        achievementCard.innerHTML = `
          <div class="achievement-icon">
            <i class="${achievement.icon}"></i>
          </div>
          <div class="achievement-info">
            <div class="achievement-name">${achievement.name}</div>
            <div class="achievement-description">${achievement.description}</div>
          </div>
        `;

        achievementsList.appendChild(achievementCard);
      });
    }
  }

  // --- Weekly Progress Bar ---
  window.updateWeeklyProgressBar = function() {
    const weeklyProgressBar = document.getElementById('weeklyProgressBar');
    if (!weeklyProgressBar) return;

    // Get the current date
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate days since the beginning of the week (Sunday)
    const daysSinceStartOfWeek = dayOfWeek;

    // Calculate progress percentage (0-100)
    const progressPercentage = (daysSinceStartOfWeek / 7) * 100;

    // Update the progress bar
    weeklyProgressBar.style.width = `${progressPercentage}%`;
  }

  // --- XP Display Update ---
  // Make updateXpDisplay a global function
  window.updateXpDisplay = function() {
    if (!userData || !userData.gamification) return;

    const xpLevel = document.querySelector('.xp-level');
    const xpBar = document.querySelector('.xp-bar');
    const xpText = document.querySelector('.xp-text');
    const streakCount = document.querySelector('.streak-count');
    const streakLabel = document.querySelector('.streak-label');

    if (!xpLevel || !xpBar || !xpText) return;

    // Update level
    xpLevel.textContent = userData.gamification.level;

    // Update XP text
    xpText.textContent = `${userData.gamification.xp} XP`;

    // Calculate progress to next level
    const currentLevel = userData.gamification.level;
    const nextLevel = currentLevel + 1;

    // Find current and next level XP requirements
    if (window.gamification && window.gamification.XP_LEVELS) {
      const currentLevelData = window.gamification.XP_LEVELS.find(l => l.level === currentLevel);
      const nextLevelData = window.gamification.XP_LEVELS.find(l => l.level === nextLevel);

      if (currentLevelData && nextLevelData) {
        const currentXp = userData.gamification.xp;
        const currentLevelXp = currentLevelData.xpRequired;
        const nextLevelXp = nextLevelData.xpRequired;

        // Calculate progress percentage
        const xpForCurrentLevel = currentXp - currentLevelXp;
        const xpRequiredForNextLevel = nextLevelXp - currentLevelXp;
        const progressPercentage = (xpForCurrentLevel / xpRequiredForNextLevel) * 100;

        // Update XP bar
        xpBar.style.width = `${progressPercentage}%`;
      } else if (currentLevel === window.gamification.XP_LEVELS.length) {
        // Max level reached
        xpBar.style.width = '100%';
      }
    } else {
      // Fallback if gamification not loaded yet
      xpBar.style.width = '0%';
    }

    // Update streak
    if (streakCount && userData.gamification.streak) {
      streakCount.textContent = userData.gamification.streak.current;
      streakLabel.textContent = userData.gamification.streak.current === 1 ? 'day streak' : 'day streak';
    }
  }

  // Voice input (Web Speech API) for answer input

  // Patch feedback and answer functions to trigger gamification and ARIA live
  const origDisplayFeedbackMessage = window.displayFeedbackMessage;
  window.displayFeedbackMessage = function(message, color) {
    origDisplayFeedbackMessage.apply(this, arguments);
    window.updateAriaLive(message);
  };
  const origDisplayAnswerFeedback = window.displayAnswerFeedback;
  window.displayAnswerFeedback = function(question, selectedOptions, selectedAnswers, isCorrect) {
    origDisplayAnswerFeedback.apply(this, arguments);
    if (typeof isCorrect === 'boolean' && userData) {
      if (isCorrect) {
        window.gamification.awardXP(10, userData);
        window.gamification.updateStreak(userData);
      } else {
        window.gamification.awardXP(1, userData);
        // Reset streak on incorrect answer
        if (userData.gamification && userData.gamification.streak) {
          userData.gamification.streak.current = 0;
          userData.gamification.streak.lastQuizDate = new Date().toDateString();
        }
      }
      // Update UI after changes
      window.gamification.updateUI();
    }
  };

  // Initialize gamification UI on load
  window.gamification.updateUI();

  // Call original init
  init(); // Call init after DOM is loaded

  // Add listener for the back button in the select question container
  const returnHomeFromSelectBtn = document.getElementById('returnHomeFromSelectBtn');
  if (returnHomeFromSelectBtn) {
    returnHomeFromSelectBtn.addEventListener('click', () => {
      showView('home');
      if(homeButton) homeButton.classList.add('active');
      if(statsButton) statsButton.classList.remove('active');
    });
  }

  // Add event listener for question selection list (using event delegation)
  const questionSelectionList = document.getElementById('questionSelectionList');
  if (questionSelectionList) {
    questionSelectionList.addEventListener('click', (event) => {
      const selectButton = event.target.closest('.select-question-btn');
      if (selectButton) {
        const questionId = selectButton.dataset.questionId;
        if (questionId) {
          startSingleQuestionQuiz(questionId);
        }
      }
    });
  }
});

// DOM Elements (Global Scope)
const userContainer = document.getElementById('userContainer');
const nicknameInput = document.getElementById('nicknameInput');
const apiKeyInput = document.getElementById('apiKeyInput'); // <-- Add reference for API key input
const saveNicknameBtn = document.getElementById('saveNicknameBtn');
const userInfo = document.getElementById('userInfo');
const usernameDisplay = document.getElementById('usernameDisplay');
const changeNicknameBtn = document.getElementById('changeNicknameBtn'); // Added reference
const homeContainer = document.getElementById('homeContainer');
const quizContainer = document.getElementById('quizContainer');
const statsContainer = document.getElementById('statsContainer');
const selectQuestionContainer = document.getElementById('selectQuestionContainer');
const acronymsContainer = document.getElementById('acronymsContainer'); // Added Acronyms container
const homeButton = document.getElementById('homeButton');
const statsButton = document.getElementById('statsButton');
const toggleColorModeBtn = document.getElementById('toggleColorMode');
const quizModeCards = document.querySelectorAll('.quiz-mode-card');
const categoryFilters = document.querySelectorAll('.categories-filter .filter-option');
const difficultyFilters = document.querySelectorAll('.difficulty-filter .filter-option');
const questionCountInput = document.getElementById('questionCount');

// Cache frequently used DOM elements (Quiz View - declared globally, assigned in cache function)
let questionNumberElement, progressBar, weeklyProgressBar, questionTextElement, categoryTagElement, difficultyTagElement, optionsContainerElement, resultContainerElement, favoriteBtnElement, hintContainerElement, hintToggleBtnElement, hintContentElement, checkAnswerBtnElement, nextQuestionBtnElement, timerContainerElement, timerDisplayElement, timeInfoElement, aiFeaturesBtnElement; // Added weeklyProgressBar and aiFeaturesBtnElement
// Acronym View Elements
let acronymsGridElement, nextAcronymsBtnElement, returnHomeFromAcronymsBtnElement; // Removed checkAcronymsBtnElement

// Function to cache elements based on the current view/mode
function cacheDOMElements(mode) {
  if (mode === 'quiz' || mode === 'select' || mode === 'flashcards' || mode === 'timed' || mode === 'exam' || mode === 'classic' || mode === 'ai-learning') { // Added IF condition and 'classic' mode
    questionNumberElement = document.getElementById('questionNumber');
    progressBar = document.getElementById('progressBar');
    weeklyProgressBar = document.getElementById('weeklyProgressBar');
  questionTextElement = document.getElementById('questionText');
  categoryTagElement = document.querySelector('#categoryTags .category-badge');
  difficultyTagElement = document.querySelector('#categoryTags .difficulty-badge');
  optionsContainerElement = document.getElementById('optionsContainer');
  resultContainerElement = document.getElementById('resultContainer');
  favoriteBtnElement = document.getElementById('favoriteBtn');
  hintContainerElement = document.getElementById('hintContainer');
  hintToggleBtnElement = document.getElementById('hintToggle');
  hintContentElement = document.getElementById('hintContent');
  checkAnswerBtnElement = document.getElementById('checkAnswerBtn');
  nextQuestionBtnElement = document.getElementById('nextQuestionBtn'); // This might be the acronym button later
  timerContainerElement = document.getElementById('timerContainer');
  timerDisplayElement = document.getElementById('timer');
  timeInfoElement = quizContainer.querySelector('#timeInfo');
  aiFeaturesBtnElement = document.getElementById('aiFeaturesBtn'); // Cache the new AI button

  // Re-attach listeners to newly created/cached elements within quiz container
  if (checkAnswerBtnElement) checkAnswerBtnElement.addEventListener('click', checkAnswer);
  // Only attach nextQuestion listener if the button exists and is NOT in select/acronym mode
  if (nextQuestionBtnElement && currentMode !== 'select' && currentMode !== 'acronyms') {
      nextQuestionBtnElement.addEventListener('click', nextQuestion);
  }
  // Add listener for the dynamically added acronym button
  if (nextQuestionBtnElement && currentMode === 'acronyms') {
      nextQuestionBtnElement.addEventListener('click', nextQuestion); // Re-use nextQuestion logic for now
  }
  if (favoriteBtnElement) favoriteBtnElement.addEventListener('click', toggleFavorite);
  if (hintToggleBtnElement) hintToggleBtnElement.addEventListener('click', toggleHint);
  if (aiFeaturesBtnElement) aiFeaturesBtnElement.addEventListener('click', handleAiFeaturesClick); // Add listener for AI button

  // Add event delegation for options container (for radio/checkbox)
  if (optionsContainerElement && !optionsContainerElement.classList.contains('matching-options') && !quizSettings?.isAcronymMode) { // Added check for acronym mode
    // Clear previous listeners if any (simple approach)
    optionsContainerElement.replaceWith(optionsContainerElement.cloneNode(true));
    optionsContainerElement = document.getElementById('optionsContainer'); // Re-cache after cloning

    optionsContainerElement.addEventListener('click', (event) => {
      const targetLabel = event.target.closest('label');
      if (targetLabel && optionsContainerElement.contains(targetLabel)) {
          const input = targetLabel.querySelector('input[name="option"]');
          if (input) {
              console.log(`Option associated with clicked label: ${input.value}`);
          }
      }
    });
  }
  // Drag and drop listeners are added dynamically in createMatchingHTML, createMatchingCategoriesHTML, and createOrderingHTML
  } // Close the IF block for quiz modes
  else if (mode === 'acronyms') {
      console.log("Caching DOM elements for acronyms mode...");

      // Cache all required elements
      acronymsGridElement = document.getElementById('acronymsGrid');
      nextAcronymsBtnElement = document.getElementById('nextAcronymsBtn');
      returnHomeFromAcronymsBtnElement = document.getElementById('returnHomeFromAcronymsBtn');

      // Special handling for AI button
      aiFeaturesBtnElement = document.getElementById('aiFeaturesBtn');

      // Log element status
      console.log("acronymsGridElement found:", !!acronymsGridElement);
      console.log("nextAcronymsBtnElement found:", !!nextAcronymsBtnElement);
      console.log("returnHomeFromAcronymsBtnElement found:", !!returnHomeFromAcronymsBtnElement);
      console.log("aiFeaturesBtnElement found in acronym mode:", !!aiFeaturesBtnElement);

      // Attach listeners for acronym buttons
      if (nextAcronymsBtnElement) {
          // Clear existing listeners
          const newNextBtn = nextAcronymsBtnElement.cloneNode(true);
          if (nextAcronymsBtnElement.parentNode) {
              nextAcronymsBtnElement.parentNode.replaceChild(newNextBtn, nextAcronymsBtnElement);
          }
          nextAcronymsBtnElement = newNextBtn;
          nextAcronymsBtnElement.addEventListener('click', loadNextAcronymSet);
      }

      if (returnHomeFromAcronymsBtnElement) {
          // Clear existing listeners
          const newHomeBtn = returnHomeFromAcronymsBtnElement.cloneNode(true);
          if (returnHomeFromAcronymsBtnElement.parentNode) {
              returnHomeFromAcronymsBtnElement.parentNode.replaceChild(newHomeBtn, returnHomeFromAcronymsBtnElement);
          }
          returnHomeFromAcronymsBtnElement = newHomeBtn;
          returnHomeFromAcronymsBtnElement.addEventListener('click', () => {
              showView('home');
              if(homeButton) homeButton.classList.add('active');
              if(statsButton) statsButton.classList.remove('active');
          });
      }

      // Special handling for AI button - we already attached a listener in resetAcronymsContainer
      // but we'll check and add one here too as a fallback
      if (aiFeaturesBtnElement && !aiFeaturesBtnElement.hasAttribute('data-has-listener')) {
          console.log("Adding click listener to AI button in acronym mode (from cacheDOMElements)");
          aiFeaturesBtnElement.setAttribute('data-has-listener', 'true');
          aiFeaturesBtnElement.addEventListener('click', function(e) {
              e.preventDefault(); // Prevent any default action
              console.log("AI button clicked from cacheDOMElements handler");
              handleAiFeaturesClick();
          });
      }
  }
}

// --- Navigation and UI ---

// --- User Nickname Handling ---
const ALL_USERS_STORAGE_KEY = 'quizAppUsers'; // Key for the object holding all users' data
let currentNickname = null; // Store the current user's nickname globally

function saveNickname() {
  const nickname = nicknameInput.value.trim();
  const apiKey = apiKeyInput.value.trim(); // <-- Get API Key

  if (nickname) {
    localStorage.setItem('quizUserNickname', nickname); // Still store the *last* used nickname for convenience
    // WARNING: Storing API keys in localStorage is insecure. Consider prompting each time or using a backend proxy in production.
    if (apiKey) {
      localStorage.setItem('geminiApiKey', apiKey);
    } else {
      localStorage.removeItem('geminiApiKey'); // Remove if empty
    }
    currentNickname = nickname; // Set the global nickname
    usernameDisplay.textContent = nickname;
    userContainer.classList.add('hidden');

    loadUserData(nickname); // Load or initialize data for this user

    // Show the appropriate initial view (home) after saving nickname
    showView('home');
    if (homeButton) homeButton.classList.add('active'); // Ensure home button is active
    if (statsButton) statsButton.classList.remove('active');

    // Update UI based on newly loaded data
    updateStatsDisplay();
    updateFavoritesDisplay();
    applyFilters(); // Apply filters based on potentially new difficulty overrides
    if (questionCountInput) {
       questionCountInput.addEventListener('change', handleFilterChange);
    }

  } else {
    showFeedback('Please enter a nickname.');
  }
}

function loadAndDisplayNickname() {
  const savedNickname = localStorage.getItem('quizUserNickname');
  const savedApiKey = localStorage.getItem('geminiApiKey'); // <-- Load API Key

  if (savedNickname) {
    currentNickname = savedNickname; // Set the global nickname
    usernameDisplay.textContent = savedNickname;
    if (apiKeyInput && savedApiKey) {
        apiKeyInput.value = savedApiKey; // <-- Pre-fill API key input if found
    }
    userContainer.classList.add('hidden');

    loadUserData(savedNickname); // Load data for this user

    // Show home container by default if nickname exists
    homeContainer.classList.remove('hidden');
    if (homeButton) homeButton.classList.add('active');

    // Update UI based on loaded data
    updateStatsDisplay();
    updateFavoritesDisplay();
    applyFilters(); // Apply filters based on loaded difficulty overrides
    if (questionCountInput) {
       questionCountInput.addEventListener('change', handleFilterChange);
    }

  } else {
    // If no nickname, ensure user container is shown and others are hidden
    currentNickname = null;
    userContainer.classList.remove('hidden');
    homeContainer.classList.add('hidden');
    quizContainer.classList.add('hidden');
    statsContainer.classList.add('hidden');
    if (selectQuestionContainer) selectQuestionContainer.classList.add('hidden'); // Hide select container too
    if (homeButton) homeButton.classList.remove('active'); // No tab active initially
    if (statsButton) statsButton.classList.remove('active');
  }
}

// Function to show the nickname input screen
function promptChangeNickname() {
    // Hide main content views
    homeContainer.classList.add('hidden');
    quizContainer.classList.add('hidden');
    statsContainer.classList.add('hidden');
    if (selectQuestionContainer) selectQuestionContainer.classList.add('hidden'); // Hide select container too
    // Show the user input container
    userContainer.classList.remove('hidden');
    nicknameInput.value = ''; // Clear the input field
    nicknameInput.focus(); // Focus the input field
    // Deactivate tab buttons
    if (homeButton) homeButton.classList.remove('active');
    if (statsButton) statsButton.classList.remove('active');
}

// Add listener for the save button
if (saveNicknameBtn) {
  saveNicknameBtn.addEventListener('click', saveNickname);
}
// Add listener for the change nickname button
if (changeNicknameBtn) {
    changeNicknameBtn.addEventListener('click', promptChangeNickname);
}
// Add listener for Enter key in nickname input
if (nicknameInput) {
    nicknameInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            saveNickname();
        }
    });
}


// --- Navigation and UI ---

// Tab Navigation
if (homeButton) {
    homeButton.addEventListener('click', () => {
      // Only allow navigation if nickname is set
      if (localStorage.getItem('quizUserNickname')) {
          showView('home');
          homeButton.classList.add('active');
          if (statsButton) statsButton.classList.remove('active');
      } else {
          showFeedback('Please save your nickname first.');
      }
    });
}

if (statsButton) {
    statsButton.addEventListener('click', () => {
      // Only allow navigation if nickname is set
      if (localStorage.getItem('quizUserNickname')) {
          showView('stats');
          if (homeButton) homeButton.classList.remove('active');
          statsButton.classList.add('active');
          updateStatsDisplay(); // Update stats when tab is clicked
      } else {
          showFeedback('Please save your nickname first.');
      }
    });
}

// Function to show the correct container (respects nickname status)
function showView(viewName) {
    // Always hide all main content sections first
    homeContainer.classList.add('hidden');
    quizContainer.classList.add('hidden');
    statsContainer.classList.add('hidden');
    if (selectQuestionContainer) selectQuestionContainer.classList.add('hidden');
    if (acronymsContainer) acronymsContainer.classList.add('hidden'); // Hide acronyms container

    // Hide performance question container if it exists
    const performanceContainer = document.getElementById('performanceQuestionContainer');
    if (performanceContainer) {
        performanceContainer.classList.add('hidden');
    }

    // If no nickname is saved, only the user container should be visible (handled in loadAndDisplayNickname)
    if (!localStorage.getItem('quizUserNickname')) {
        userContainer.classList.remove('hidden');
        return; // Don't show other views if nickname isn't set
    }

    // If nickname is set, show the requested view
    userContainer.classList.add('hidden'); // Ensure user container is hidden
    if (viewName === 'home') {
        homeContainer.classList.remove('hidden');
        updateDailyProgressBars(); // Update progress when showing home
        updateSectionProgressBars(); // Update section progress when showing home
        updateDifficultyProgressBars();
        updateXpDisplay(); // Update XP display

    } else if (viewName === 'quiz') {
        quizContainer.classList.remove('hidden');
        updateWeeklyProgressBar(); // Update weekly progress bar when showing quiz
        updateXpDisplay(); // Update XP display
    } else if (viewName === 'stats') {
        statsContainer.classList.remove('hidden');
    } else if (viewName === 'select') { // Added
        if (selectQuestionContainer) selectQuestionContainer.classList.remove('hidden');
    } else if (viewName === 'acronyms') { // Added acronyms view
        if (acronymsContainer) acronymsContainer.classList.remove('hidden');
    } else if (viewName === 'performance') { // Add support for performance view
        if (performanceContainer) {
            performanceContainer.classList.remove('hidden');
        }
    }
}

// Make showView available globally for other modules
window.showView = showView;


// Toggle Color Mode
if (toggleColorModeBtn) {
    // Initialize color mode from localStorage
    const savedMode = localStorage.getItem('colorMode');
    const body = document.body;
    function updateColorModeUI(mode) {
        if (mode === 'dark') {
            body.classList.add('dark-mode');
            toggleColorModeBtn.textContent = 'Switch to Light Mode';
        } else {
            body.classList.remove('dark-mode');
            toggleColorModeBtn.textContent = 'Switch to Dark Mode';
        }
    }
    // Set initial mode
    if (savedMode === 'dark' || savedMode === 'light') {
        updateColorModeUI(savedMode);
    } else {
        // Default: use current class on body
        updateColorModeUI(body.classList.contains('dark-mode') ? 'dark' : 'light');
    }
    // Toggle handler
    toggleColorModeBtn.addEventListener('click', () => {
        const isDark = body.classList.toggle('dark-mode');
        const newMode = isDark ? 'dark' : 'light';
        localStorage.setItem('colorMode', newMode);
        updateColorModeUI(newMode);
    });
}

// --- Filters ---

// Quiz Mode Selection
quizModeCards.forEach(card => {
  card.addEventListener('click', () => {
    const mode = card.dataset.mode;
    // Wrap the async call to avoid the listener returning a promise implicitly
    // and add error handling.
    (async () => {
        try {
            await startQuiz(mode);
        } catch (error) {
            console.error(`Error starting quiz mode ${mode}:`, error);
            showFeedback(`Failed to start ${mode} mode. Check console.`);
            // Optionally return to home view on error
             showView('home');
             if(homeButton) homeButton.classList.add('active');
             if(statsButton) statsButton.classList.remove('active');
        }
    })();
  });
});

// Category & Difficulty Filters
categoryFilters.forEach(filter => {
  filter.addEventListener('click', () => {
    categoryFilters.forEach(f => f.classList.remove('active'));
    filter.classList.add('active');
    applyFilters();
  });
});

difficultyFilters.forEach(filter => {
  filter.addEventListener('click', () => {
    difficultyFilters.forEach(f => f.classList.remove('active'));
    filter.classList.add('active');
    applyFilters();
  });
});

function applyFilters() {
  const selectedCategory = document.querySelector('.categories-filter .active')?.dataset.category || 'all';
  const selectedDifficulty = document.querySelector('.difficulty-filter .active')?.dataset.difficulty || 'all';

  // This would filter the quiz mode cards based on selected filters (Future enhancement)
  console.log(`Filtering for category: ${selectedCategory}, difficulty: ${selectedDifficulty}`);

  // For now, just simulating filtering by showing all cards
  quizModeCards.forEach(card => {
    card.classList.remove('hidden'); // Use class instead of style
  });
  handleFilterChange(); // Update available question count on filter change
}

// --- Quiz State ---
let currentQuestions = [];
let currentQuestionIndex = 0;
let currentMode = '';
let correctAnswers = 0;
let quizStartTime = 0;
let questionStartTime = 0;
let timerInterval = null;
let examTimerInterval = null;
let examTimeRemaining = 0;
let quizSettings = {
  timedMode: false,
  timePerQuestion: 30,
  totalTime: 300,
  showHints: false,
  questionsCount: 10
};
let currentQuizAnswers = {};
let aiSessionQuestionHistory = []; // <-- Add global variable for AI session history

// Mock Questions Data (loaded later)
const mockQuestions = [
  { id: "A1", category: 'A', question: "What is the capital of France?", options: ["London", "Paris", "Berlin", "Madrid"], answer: "Paris", explanation: "Paris is the capital of France.", difficulty: 'easy' },
  { id: "A2", category: 'A', question: "Which planet is known as the Red Planet?", options: ["Earth", "Mars", "Jupiter", "Venus"], answer: "Mars", explanation: "Mars is known as the Red Planet due to its reddish appearance.", difficulty: 'easy' },
  { id: "A3", category: 'A', question: "What is the largest ocean on Earth?", options: ["Atlantic Ocean", "Indian Ocean", "Arctic Ocean", "Pacific Ocean"], answer: "Pacific Ocean", explanation: "The Pacific Ocean is the largest and deepest ocean on Earth.", difficulty: 'medium' },
  { id: "A4", category: 'A', question: "Who wrote 'Romeo and Juliet'?", options: ["Charles Dickens", "William Shakespeare", "Jane Austen", "Mark Twain"], answer: "William Shakespeare", explanation: "Romeo and Juliet was written by William Shakespeare.", difficulty: 'easy' },
  { id: "A5", category: 'A', question: "What is the chemical symbol for gold?", options: ["Go", "Gd", "Au", "Ag"], answer: "Au", explanation: "Au (from Latin 'aurum') is the chemical symbol for gold.", difficulty: 'medium' },
  { id: "A6", category: 'A', question: "Which of these is a prime number?", options: ["1", "2", "4", "6"], answer: "2", explanation: "2 is the only even prime number.", difficulty: 'easy' },
  { id: "A7", category: 'A', question: "What is the hardest natural substance on Earth?", options: ["Gold", "Iron", "Diamond", "Platinum"], answer: "Diamond", explanation: "Diamond is the hardest naturally occurring substance.", difficulty: 'hard' },
  { id: "B1", category: 'B', question: "Which elements are noble gases?", options: ["Oxygen", "Helium", "Neon", "Nitrogen"], answer: ["Helium", "Neon"], "multiple answers": 2, explanation: "Helium and Neon are both noble gases.", difficulty: 'multiple choice' },
  { id: "B2", category: 'B', question: "What is the capital of Japan?", options: ["Beijing", "Seoul", "Tokyo", "Bangkok"], answer: "Tokyo", explanation: "Tokyo is the capital of Japan.", difficulty: 'easy' },
  { id: "B3", category: 'B', question: "Which of these countries is in Europe?", options: ["Egypt", "Brazil", "Australia", "France"], answer: "France", explanation: "France is located in Western Europe.", difficulty: 'easy' },
  { id: "B4", category: 'B', question: "What is the largest mammal?", options: ["Elephant", "Blue Whale", "Giraffe", "Hippopotamus"], answer: "Blue Whale", explanation: "The Blue Whale is the largest mammal on Earth.", difficulty: 'medium' },
  { id: "B5", category: 'B', question: "Which of these is NOT a programming language?", options: ["Java", "Python", "Cobra", "Dolphin"], answer: "Dolphin", explanation: "Dolphin is not a programming language.", difficulty: 'medium' },
  { id: "C1", category: 'C', question: "What is the square root of 64?", options: ["6", "8", "10", "12"], answer: "8", explanation: "8 × 8 = 64, so 8 is the square root of 64.", difficulty: 'easy' },
  { id: "C2", category: 'C', question: "Which of these is a primary color?", options: ["Green", "Orange", "Purple", "Red"], answer: "Red", explanation: "Red is a primary color along with blue and yellow.", difficulty: 'easy' },
  { id: "C3", category: 'C', question: "What is the main component of air?", options: ["Oxygen", "Carbon Dioxide", "Nitrogen", "Hydrogen"], answer: "Nitrogen", explanation: "Nitrogen makes up about 78% of Earth's atmosphere.", difficulty: 'medium' },
  { id: "M1", category: 'A', question: "Which are prime numbers?", options: ["2", "4", "5", "6"], answer: ["2", "5"], "multiple answers": 2, explanation: "2 and 5 are prime numbers.", difficulty: 'multiple choice' },
  { id: "M2", category: 'B', question: "Which of these are mammals?", options: ["Shark", "Dolphin", "Eagle", "Bat"], answer: ["Dolphin", "Bat"], "multiple answers": 2, explanation: "Dolphins and bats are mammals, while sharks and eagles are not.", difficulty: 'multiple choice' },
  { id: "M3", category: 'C', question: "Which of these are vegetables?", options: ["Apple", "Carrot", "Broccoli", "Banana"], answer: ["Carrot", "Broccoli"], "multiple answers": 2, explanation: "Carrots and broccoli are vegetables, while apples and bananas are fruits.", difficulty: 'multiple choice' }
];
let allQuestions = []; // Will be populated by fetchQuestions
let allAcronyms = []; // Will hold acronym data

// --- Quiz Initialization ---

// Gets settings object based on the selected mode
function getQuizSettingsForMode(mode) {
    switch(mode) {
        case 'classic':
          return { timedMode: false, showHints: false, questionsCount: 10, showExplanation: true, randomOrder: false };
        case 'timed':
          return { timedMode: true, timePerQuestion: 20, totalTime: 0, showHints: false, questionsCount: 15, showExplanation: false, randomOrder: true };
        case 'exam':
          // Exam time is calculated later based on question count
          return { timedMode: true, timePerQuestion: 0, totalTime: 600, showHints: false, questionsCount: 20, showExplanation: false, randomOrder: true, reviewMode: true, examMode: true, hideImmediateFeedback: true };
        case 'flashcards':
          return { timedMode: false, showHints: true, questionsCount: 15, showExplanation: true, randomOrder: true, flashcardMode: true };
        case 'select': // Added select mode settings
          return { timedMode: false, showHints: true, questionsCount: 1, showExplanation: true, randomOrder: false, selectMode: true };
        case 'acronyms': // Added acronym mode settings
          // Start as flashcards, can enhance later
          return { timedMode: false, showHints: false, questionsCount: 10, showExplanation: false, randomOrder: true, flashcardMode: true, isAcronymMode: true };
        case 'ai-learning': // Added AI Learning mode settings
          return { timedMode: false, showHints: false, questionsCount: 1, showExplanation: true, randomOrder: false, isAiMode: true };
        case 'interactive': // Interactive Challenges mode settings (disabled)
          return { timedMode: false, showHints: true, questionsCount: 5, showExplanation: true, randomOrder: true, isInteractiveMode: true, isDisabled: true };
        case 'performance': // Performance-based question mode settings
          return { timedMode: false, showHints: true, questionsCount: 10, showExplanation: true, randomOrder: true, isPerformanceMode: true };
        default:
          console.warn(`Unknown quiz mode: ${mode}. Defaulting to classic.`);
          return { timedMode: false, showHints: false, questionsCount: 10, showExplanation: true, randomOrder: false };
      }
}

// Start Quiz Function
function resetQuizContainer() {
  // Rebuilds the quiz container's inner HTML.
  quizContainer.innerHTML = `
    <div id="quizHeader">
      <div class="progress-info">
        <span id="questionNumber">Question 1/10</span>
        <span id="timeInfo" class="time-indicator"></span>
      </div>
      <div class="progress-bar">
        <div id="progressBar" class="progress"></div>
      </div>
      <div class="weekly-progress-bar">
        <div id="weeklyProgressBar" class="weekly-progress"></div>
      </div>
    </div>

    <div id="questionContainer">
      <div class="question-favorites">
        <button id="favoriteBtn" class="favorite-btn">★</button>
      </div>
      <div id="categoryTags">
        <span class="category-badge">Section A</span>
        <span class="difficulty-badge difficulty-medium">Medium</span>
      </div>
      <h2 id="questionText">Loading question...</h2>

      <div id="timerContainer" class="hidden"> <!-- Use class -->
        <div id="timer">30</div>
      </div>

      <div id="optionsContainer" class="options">
        <!-- Options will be inserted here -->
      </div>

      <div id="resultContainer"></div>

      <div class="help-hint hidden" id="hintContainer"> <!-- Use class -->
        <button id="hintToggle" class="hint-toggle">Show Hint</button>
        <div id="hintContent" class="hint-content hidden"> <!-- Use class -->
          This hint will help you answer the question correctly.
        </div>
      </div>

      <div class="buttons">
        <button id="checkAnswerBtn" class="button"><i class="fa-solid fa-eye"></i></button> <!-- Use icon -->
        <button id="nextQuestionBtn" class="button" disabled>Next Question</button>
        <button id="aiFeaturesBtn" class="button hidden" aria-label="AI Features"><i class="fa-solid fa-robot"></i> AI Tools</button> <!-- Add button back -->
      </div>
    </div>
  `;
  // Listeners are re-attached in cacheQuizDOMElements after this runs
}

// Function to reset the container specifically for Acronym Learning Mode
function resetAcronymsContainer() {
    console.log("Resetting acronyms container");

    // Clear the container first
    acronymsContainer.innerHTML = '';

    // Create and append elements one by one for better control
    const heading = document.createElement('h2');
    heading.textContent = 'Acronym Flashcards';
    acronymsContainer.appendChild(heading);

    // Create acronyms grid
    const grid = document.createElement('div');
    grid.id = 'acronymsGrid';
    grid.className = 'acronym-grid';
    const loadingText = document.createElement('p');
    loadingText.textContent = 'Loading acronyms...';
    grid.appendChild(loadingText);
    acronymsContainer.appendChild(grid);

    // Create AI result container
    const aiResultContainer = document.createElement('div');
    aiResultContainer.id = 'aiResultContainer';
    aiResultContainer.style.marginTop = '15px';
    aiResultContainer.style.padding = '10px';
    aiResultContainer.style.backgroundColor = 'var(--card-bg-color, #f9f9f9)';
    aiResultContainer.style.border = '1px solid var(--border-color, #ddd)';
    aiResultContainer.style.borderRadius = '5px';
    aiResultContainer.style.maxHeight = '400px';
    aiResultContainer.style.overflowY = 'auto';
    acronymsContainer.appendChild(aiResultContainer);

    // Create buttons container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'buttons';

    // Create Next Set button
    const nextButton = document.createElement('button');
    nextButton.id = 'nextAcronymsBtn';
    nextButton.className = 'button';
    nextButton.disabled = true;
    nextButton.textContent = 'Next Set';
    buttonsContainer.appendChild(nextButton);

    // Create Return Home button
    const homeButton = document.createElement('button');
    homeButton.id = 'returnHomeFromAcronymsBtn';
    homeButton.className = 'button button-secondary';
    homeButton.textContent = 'Return Home';
    buttonsContainer.appendChild(homeButton);

    // Create AI Features button
    const aiButton = document.createElement('button');
    aiButton.id = 'aiFeaturesBtn';
    aiButton.className = 'button';
    aiButton.setAttribute('aria-label', 'AI Features');
    aiButton.innerHTML = '<i class="fa-solid fa-robot"></i> AI Tools';

    // Force visibility with multiple approaches
    aiButton.style.display = 'inline-block';
    aiButton.style.visibility = 'visible';
    aiButton.style.opacity = '1';
    aiButton.style.position = 'static';
    aiButton.style.marginLeft = '10px'; // Add some spacing

    // Add click handler directly here
    aiButton.onclick = function() {
        console.log("AI button clicked directly from inline handler");
        handleAiFeaturesClick();
        return false; // Prevent default
    };

    buttonsContainer.appendChild(aiButton);
    acronymsContainer.appendChild(buttonsContainer);

    console.log("Acronyms container reset complete");
    console.log("AI button added with ID:", aiButton.id);
}

// Make startQuiz async to handle AI generation
async function startQuiz(mode) {
  console.log(`Starting startQuiz with mode: ${mode}`); // <-- Log entry and mode

  currentMode = mode;
  quizSettings = getQuizSettingsForMode(mode); // Get settings for the mode

  // --- AI Learning Mode (Handle First - Generates multiple questions) ---
  if (mode === 'ai-learning') {
    console.log("Starting AI Learning mode...");
    aiSessionQuestionHistory = []; // Clear history for new session
    resetQuizContainer();
    cacheDOMElements(currentMode); // Cache elements after rebuilding

    // Get desired number of questions
    const requestedCount = parseInt(questionCountInput?.value) || 1; // Default to 1 if input missing/invalid
    quizSettings.questionsCount = requestedCount; // Store the count
    console.log(`AI Mode: Attempting to generate ${requestedCount} questions.`);

    // Show initial loading state
    if (questionTextElement) questionTextElement.textContent = `Generating ${requestedCount} AI question(s)... (0/${requestedCount})`;
    if (optionsContainerElement) optionsContainerElement.innerHTML = ''; // Clear options
    if (checkAnswerBtnElement) checkAnswerBtnElement.disabled = true;
    if (nextQuestionBtnElement) nextQuestionBtnElement.disabled = true;
    showView('quiz'); // Show the quiz view with loading message
    if(homeButton) homeButton.classList.remove('active');
    if(statsButton) statsButton.classList.remove('active');

    const generatedQuestions = [];
    let successCount = 0;
    let failedCount = 0;

    // Loop to generate questions
    for (let i = 0; i < requestedCount; i++) {
        // Update loading message
        if (questionTextElement) questionTextElement.textContent = `Generating ${requestedCount} AI question(s)... (${i}/${requestedCount})`;

        const aiQuestion = await generateAiQuestion(); // Await the async function

        if (aiQuestion) {
            generatedQuestions.push(aiQuestion);
            successCount++;
            console.log(`AI Question ${i + 1} generated successfully:`, aiQuestion.id);
        } else {
            failedCount++;
            console.error(`Failed to generate AI question ${i + 1}.`);
            // Optional: Stop generation early if too many failures?
            // if (failedCount > requestedCount / 2) break;
        }
    }

    // Check results of generation
    if (generatedQuestions.length > 0) {
        console.log(`AI Generation complete. Success: ${successCount}, Failed: ${failedCount}`);
        if (failedCount > 0) {
            showFeedback(`Generated ${successCount} questions. Failed to generate ${failedCount}.`);
        }

        // Setup quiz state with generated questions
        currentQuestions = generatedQuestions;
        quizSettings.questionsCount = currentQuestions.length; // Update count based on actual success
        currentQuestionIndex = 0;
        correctAnswers = 0;
        quizStartTime = Date.now();
        currentQuizAnswers = {};

        // Display the first generated question
        displayQuestion();
        // displayQuestion should re-enable check button and handle next button state

    } else {
        console.error("Failed to generate any AI questions.");
        // Keep showing the quiz container, but update text to show failure
        if (questionTextElement) questionTextElement.textContent = `Failed to generate any AI questions (tried ${requestedCount}). Please check your API key or try again later.`;
        // Optionally add a button to return home
        const buttonsContainer = quizContainer.querySelector('.buttons');
        if (buttonsContainer && !buttonsContainer.querySelector('#aiFailHomeBtn')) {
             const homeBtn = document.createElement('button');
             homeBtn.id = 'aiFailHomeBtn';
             homeBtn.textContent = 'Return Home';
             homeBtn.className = 'button';
             homeBtn.onclick = () => {
                  showView('home');
                  if(homeButton) homeButton.classList.add('active');
                  if(statsButton) statsButton.classList.remove('active');
             };
             // Hide default buttons if they exist
             if(checkAnswerBtnElement) checkAnswerBtnElement.style.display = 'none';
             if(nextQuestionBtnElement) nextQuestionBtnElement.style.display = 'none';
             buttonsContainer.appendChild(homeBtn);
        }
    }
    return; // Exit after handling AI mode
  }

  // --- Select Question Mode (Handle Second) ---
  else if (mode === 'select') {
    // Check if questions are loaded for select mode
    if (!allQuestions || allQuestions.length === 0) {
        console.error('Questions not loaded yet for select mode');
        showFeedback('Loading questions, please wait...');
        try {
            await fetchQuestions();
            if (allQuestions && allQuestions.length > 0) {
                startQuiz(mode); // Retry
            } else {
                showFeedback('Failed to load questions. Cannot use select mode.');
                showView('home');
                if(homeButton) homeButton.classList.add('active');
                if(statsButton) statsButton.classList.remove('active');
            }
        } catch (error) {
            showFeedback(`Error loading data: ${error.message}`);
            showView('home');
            if(homeButton) homeButton.classList.add('active');
            if(statsButton) statsButton.classList.remove('active');
        }
        return;
    }

    // Proceed with select mode logic
    console.log("Getting filtered questions for select mode");
    let filteredQuestions = getFilteredQuestions(); // Get questions based on filters

    // If no questions after filtering, try without time filtering
    if (filteredQuestions.length === 0) {
      console.warn("No questions available after filtering for select mode. Trying without time filtering.");

      // Get questions with only category/difficulty filters (no time filtering)
      const selectedCategory = document.querySelector('.categories-filter .active')?.dataset.category || 'all';
      const selectedDifficulty = document.querySelector('.difficulty-filter .active')?.dataset.difficulty || 'all';

      filteredQuestions = [...allQuestions];

      // Apply Category Filter
      if (selectedCategory !== 'all') {
        filteredQuestions = filteredQuestions.filter(q => q.category === selectedCategory);
      }

      // Apply Difficulty Filter
      if (selectedDifficulty !== 'all') {
        filteredQuestions = filteredQuestions.filter(q => {
          const originalDifficulty = q.difficulty || 'medium';
          const effectiveDifficulty = getEffectiveDifficulty(q.id);

          if (selectedDifficulty === 'hard') {
            return effectiveDifficulty === 'hard';
          }
          if (selectedDifficulty === 'multiple choice') {
            return originalDifficulty === 'multiple choice';
          }
          if (selectedDifficulty === 'performance') {
            return ['performance', 'matching', 'matching_categories', 'ordering'].includes(originalDifficulty);
          }
          return effectiveDifficulty === selectedDifficulty;
        });
      }

      console.log(`Select mode: Got ${filteredQuestions.length} questions after filtering without time restrictions`);
    }

    // If still no questions, try with all questions
    if (filteredQuestions.length === 0) {
      console.warn("No questions available even without time filtering for select mode. Using all available questions.");
      filteredQuestions = [...allQuestions];
      console.log(`Select mode: Using all ${filteredQuestions.length} questions as fallback`);

      // If we still have no questions, show error and return
      if (filteredQuestions.length === 0) {
        showFeedback('No questions available. Please check your JSON files or refresh the page.');
        showView('home');
        if(homeButton) homeButton.classList.add('active');
        if(statsButton) statsButton.classList.remove('active');
        return;
      }

      showFeedback(`Using all available questions (${filteredQuestions.length}) as no questions match your current filters.`);
    }

    displayQuestionSelectionList(filteredQuestions);
    showView('select');
    if(homeButton) homeButton.classList.remove('active');
    if(statsButton) statsButton.classList.remove('active');
    return; // Exit startQuiz for select mode
  }

  // --- Interactive Challenges Mode --- (Hidden as requested)
  else if (mode === 'interactive') {
    console.log("Interactive Challenges mode is currently disabled.");
    showFeedback('Interactive Challenges feature is currently unavailable.');
    showView('home');
    if(homeButton) homeButton.classList.add('active');
    if(statsButton) statsButton.classList.remove('active');
    return; // Exit after handling interactive mode
  }

  // --- Performance Mode ---
  else if (mode === 'performance') {
    console.log("Starting Performance mode...");

    // Check if performance integration is available
    if (typeof window.performanceIntegration === 'undefined' || !window.performanceIntegration.selectQuizMode) {
      console.error('Performance integration not available');
      showFeedback('Performance mode is not available. Please check that all performance scripts are loaded.');
      showView('home');
      if(homeButton) homeButton.classList.add('active');
      if(statsButton) statsButton.classList.remove('active');
      return;
    }

    // Use the performance integration to handle mode selection
    window.performanceIntegration.selectQuizMode('performance');
    return; // Exit after handling performance mode
  }

  // --- Remaining Modes (Classic, Timed, Exam, Flashcards, Acronyms) ---
  // Check data loading for these modes
  const questionsNeeded = !['acronyms'].includes(mode);
  const acronymsNeeded = mode === 'acronyms';
  const questionsAvailable = allQuestions && allQuestions.length > 0;
  const acronymsAvailable = allAcronyms && allAcronyms.length > 0;

  if ((questionsNeeded && !questionsAvailable) || (acronymsNeeded && !acronymsAvailable)) {
      const dataType = acronymsNeeded ? 'Acronyms' : 'Questions';
      console.error(`${dataType} not loaded yet for mode ${mode}`);
      showFeedback(`Loading ${dataType.toLowerCase()}, please wait...`);
      try {
          await fetchQuestions(); // Wait for fetch to complete
          const retryQuestionsAvailable = allQuestions && allQuestions.length > 0;
          const retryAcronymsAvailable = allAcronyms && allAcronyms.length > 0;
          if ((questionsNeeded && retryQuestionsAvailable) || (acronymsNeeded && retryAcronymsAvailable)) {
              startQuiz(mode); // Retry starting the quiz
          } else {
              showFeedback(`Failed to load ${dataType.toLowerCase()}. Please try again later.`);
              showView('home');
              if(homeButton) homeButton.classList.add('active');
              if(statsButton) statsButton.classList.remove('active');
          }
      } catch (error) {
          showFeedback(`Error loading data: ${error.message}`);
          showView('home');
          if(homeButton) homeButton.classList.add('active');
          if(statsButton) statsButton.classList.remove('active');
      }
      return; // Exit while loading
  }

  // --- Acronym Mode Logic ---
  if (mode === 'acronyms') {
      console.log("Starting Acronyms mode...");
      currentMode = 'acronyms'; // Explicitly set the mode
      console.log("Current mode set to:", currentMode);
      // resetQuizContainer(); // Incorrect: This loads the standard quiz HTML
      resetAcronymsContainer(); // Correct: Load the HTML structure for acronyms
      cacheDOMElements(currentMode); // Cache elements specific to 'acronyms' view

      // Load the first set of acronyms (moved from displayAcronyms)
      if (!loadAcronymSet()) {
          showFeedback('Failed to load initial acronym set.');
          showView('home');
          if(homeButton) homeButton.classList.add('active');
          if(statsButton) statsButton.classList.remove('active');
          return;
      }

      // currentQuestions = allAcronyms; // No longer needed, loadAcronymSet handles currentAcronymSet
      // quizSettings.questionsCount = Math.min(quizSettings.questionsCount, currentQuestions.length); // Handled by loadAcronymSet logic implicitly
      if (quizSettings.randomOrder) {
        currentQuestions = shuffleArray([...allAcronyms]).slice(0, quizSettings.questionsCount);
      } else {
        currentQuestions = [...allAcronyms].slice(0, quizSettings.questionsCount);
      }

      if (currentQuestions.length === 0) {
          showFeedback('No acronyms available.');
          showView('home');
          if(homeButton) homeButton.classList.add('active');
          if(statsButton) statsButton.classList.remove('active');
          return;
      }

      // currentQuestionIndex = 0; // Not used directly in acronym mode display
      // correctAnswers = 0; // Not tracked in acronym learning mode
      // quizStartTime = Date.now(); // Not relevant for learning mode
      // currentQuizAnswers = {}; // Not tracked

      // UI adaptation is handled within displayAcronyms and resetAcronymsContainer

      // Display the loaded acronyms
      displayAcronyms(); // Display the first set of acronym cards

      // Show the acronyms view
      showView('acronyms');
      if(homeButton) homeButton.classList.remove('active');
      if(statsButton) statsButton.classList.remove('active');
      return; // Exit startQuiz after handling acronym mode
    }
    // --- Other Regular Quiz Modes --- (Classic, Timed, Exam, Flashcards)
  else {
      console.log(`Starting regular quiz mode: ${mode}`);
      resetQuizContainer();
      cacheDOMElements(mode); // Cache elements after rebuilding

      // Try to load questions with current filters/settings
      if (!loadQuestions(mode)) {
          showView('home'); // Return to home if loading fails
          if(homeButton) homeButton.classList.add('active');
          if(statsButton) statsButton.classList.remove('active');
          return;
      }

      // Initialize quiz state
      resetQuiz();

      // Switch view & common setup
      showView('quiz');
      if(homeButton) homeButton.classList.remove('active');
      if(statsButton) statsButton.classList.remove('active');
      currentQuizAnswers = {}; // Reset current quiz answers

      // Start timer if applicable
      if (quizSettings.timedMode && quizSettings.timePerQuestion > 0) {
        startTimer(quizSettings.timePerQuestion, 'question');
      } else if (quizSettings.examMode) {
        startTimer(quizSettings.totalTime, 'exam');
      }
  }
}

// Helper function to get questions based on current filters (no shuffle/limit)
function getFilteredQuestions() {
  const selectedCategory = document.querySelector('.categories-filter .active')?.dataset.category || 'all';
  const selectedDifficulty = document.querySelector('.difficulty-filter .active')?.dataset.difficulty || 'all';

  let filteredQuestions = [...allQuestions];

  // Apply Category Filter
  if (selectedCategory !== 'all') {
    filteredQuestions = filteredQuestions.filter(q => q.category === selectedCategory);
  }

  // Apply Difficulty Filter (considering overrides)
  if (selectedDifficulty !== 'all') {
    filteredQuestions = filteredQuestions.filter(q => {
      const originalDifficulty = q.difficulty || 'medium';
      const effectiveDifficulty = getEffectiveDifficulty(q.id); // Considers user overrides

      if (selectedDifficulty === 'hard') {
        return effectiveDifficulty === 'hard';
      }
      if (selectedDifficulty === 'multiple choice') {
        return originalDifficulty === 'multiple choice'; // Match original type
      }
      if (selectedDifficulty === 'performance') { // Added check for performance
        // Include matching, ordering, etc.
        return ['performance', 'matching', 'matching_categories', 'ordering'].includes(originalDifficulty);
      }
      // For other specific difficulties (e.g., 'easy' if re-added), match the effective difficulty
      return effectiveDifficulty === selectedDifficulty;
    });
  }

  // --- Add timestamp-based filtering ---
  console.log(`Starting with ${filteredQuestions.length} questions after category/difficulty filters for selection.`);

  // Check if we have any questions to filter
  if (filteredQuestions.length === 0) {
    console.warn("No questions available after category/difficulty filtering. Skipping time-based filtering.");
    return filteredQuestions; // Return early if no questions to filter
  }

  // Check if userData and questionHistory exist
  if (!userData || !userData.questionHistory) {
    console.warn("User data or question history not available. Skipping time-based filtering.");
    return filteredQuestions; // Return early if no user data
  }

  const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000); // Changed from 24 hours to 7 days
  let recentlyAnsweredCount = 0;
  let questionsFilteredByTime = filteredQuestions.filter(q => {
      const historyEntry = userData.questionHistory[q.id];
      // Check if answered correctly AND recently (within the last week)
      const answeredCorrectlyAndRecently = historyEntry &&
                                           historyEntry.correct === true && // Check if correct
                                           historyEntry.timestamp &&
                                           historyEntry.timestamp >= oneWeekAgo; // Use oneWeekAgo

      if (answeredCorrectlyAndRecently) {
          console.log(`Excluding question ID ${q.id} (answered *correctly* within the last week at ${new Date(historyEntry.timestamp).toLocaleString()})`); // Updated log message
          recentlyAnsweredCount++;
      }
      // Include if NOT answered correctly and recently
      return !answeredCorrectlyAndRecently;
  });
  console.log(`Excluded ${recentlyAnsweredCount} questions answered *correctly* within the last week for selection.`); // Updated log message

  // If filtering by time leaves no questions, fall back to the original filtered list
  if (questionsFilteredByTime.length === 0 && filteredQuestions.length > 0) {
      console.log("All available questions answered recently. Re-including all filtered questions for selection.");
      // Revert to the list before time filtering
      questionsFilteredByTime = filteredQuestions;
  }

  // Use the potentially time-filtered list
  filteredQuestions = questionsFilteredByTime;
  console.log(`Returning ${filteredQuestions.length} questions for selection after time filtering.`);
  // --- End timestamp-based filtering ---

  return filteredQuestions; // Return the full filtered list
}


// Load questions based on mode and filters (for regular quizzes)
function loadQuestions(mode) {
  // --- Regular Question Loading ---
  const customCount = parseInt(questionCountInput.value);

  if (isNaN(customCount) || customCount < 1) {
    showFeedback('Please enter a valid number of questions');
    return false;
  }

  // Check if allQuestions is loaded and has content
  if (!allQuestions || allQuestions.length === 0) {
    console.error("No questions loaded in allQuestions array");
    showFeedback("Error: No questions loaded. Please refresh the page and try again.");
    return false;
  }

  // Get filtered questions based on current filters
  let filteredQuestions = getFilteredQuestions(); // Use the helper to get filtered list
  console.log(`loadQuestions: Got ${filteredQuestions.length} questions after filtering`);

  // If no questions after filtering, try without time filtering
  if (filteredQuestions.length === 0) {
    console.warn("No questions available after filtering. Trying without time filtering.");

    // Get questions with only category/difficulty filters (no time filtering)
    const selectedCategory = document.querySelector('.categories-filter .active')?.dataset.category || 'all';
    const selectedDifficulty = document.querySelector('.difficulty-filter .active')?.dataset.difficulty || 'all';

    filteredQuestions = [...allQuestions];

    // Apply Category Filter
    if (selectedCategory !== 'all') {
      filteredQuestions = filteredQuestions.filter(q => q.category === selectedCategory);
    }

    // Apply Difficulty Filter
    if (selectedDifficulty !== 'all') {
      filteredQuestions = filteredQuestions.filter(q => {
        const originalDifficulty = q.difficulty || 'medium';
        const effectiveDifficulty = getEffectiveDifficulty(q.id);

        if (selectedDifficulty === 'hard') {
          return effectiveDifficulty === 'hard';
        }
        if (selectedDifficulty === 'multiple choice') {
          return originalDifficulty === 'multiple choice';
        }
        if (selectedDifficulty === 'performance') {
          return ['performance', 'matching', 'matching_categories', 'ordering'].includes(originalDifficulty);
        }
        return effectiveDifficulty === selectedDifficulty;
      });
    }

    console.log(`loadQuestions: Got ${filteredQuestions.length} questions after filtering without time restrictions`);
  }

  // If still no questions, try with all questions
  if (filteredQuestions.length === 0) {
    console.warn("No questions available even without time filtering. Using all available questions.");
    filteredQuestions = [...allQuestions];
    console.log(`loadQuestions: Using all ${filteredQuestions.length} questions as fallback`);

    // If we still have no questions, show error and return
    if (filteredQuestions.length === 0) {
      showFeedback(`No questions available. Please check your JSON files or refresh the page.`);
      return false;
    }

    showFeedback(`Using all available questions (${filteredQuestions.length}) as no questions match your current filters.`);
  }

  // Adjust question count if needed
  if (filteredQuestions.length < customCount) {
    showFeedback(`Only ${filteredQuestions.length} questions available. Using ${filteredQuestions.length} questions.`);
    quizSettings.questionsCount = filteredQuestions.length;
  } else {
    quizSettings.questionsCount = customCount;
  }

  // Shuffle and select the final set of questions
  filteredQuestions = shuffleArray([...filteredQuestions]);
  currentQuestions = filteredQuestions.slice(0, quizSettings.questionsCount);
  console.log(`loadQuestions: Final selection has ${currentQuestions.length} questions`);

  // Adjust exam time based on actual question count
  if (mode === 'exam') {
    quizSettings.totalTime = quizSettings.questionsCount * 30; // e.g., 30 seconds per question
    examTimeRemaining = quizSettings.totalTime;
  }
  return true;
}

// Improved Fisher-Yates shuffle algorithm
function shuffleArray(array) {
  let currentIndex = array.length, randomIndex;
  while (currentIndex > 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
  }
  return array;
}

// Reset quiz state variables
function resetQuiz() {
  currentQuestionIndex = 0;
  correctAnswers = 0;
  quizStartTime = Date.now();
  displayQuestion(); // Display the first question

  // Standardize progress bar behavior across all modes
  if (progressBar) {
    progressBar.style.width = `${((currentQuestionIndex + 1) / currentQuestions.length) * 100}%`;
    // Add animation class for visual feedback
    progressBar.classList.add('progress-bar-reset');
    setTimeout(() => {
      progressBar.classList.remove('progress-bar-reset');
    }, 500);
  }

  // Update weekly progress bar
  updateWeeklyProgressBar();

  // Timer starting is now handled in startQuiz after resetQuiz completes.
}

// --- Displaying Questions ---

// Format question text (handles line breaks)
function formatQuestionText(text) {
  return text ? text.replace(/\n/g, '<br>') : 'Question text missing';
}

// Creates and populates the options elements for Radio/Checkbox
function createOptionsHTML(question) {
    if (!optionsContainerElement) return;
    optionsContainerElement.classList.remove('matching-options', 'matching-dnd-container', 'ordering-container'); // Ensure matching/ordering classes are removed
    optionsContainerElement.classList.add('options'); // Ensure grid layout for non-matching
    optionsContainerElement.innerHTML = ''; // Clear previous options

    // Check if this is flashcard mode - if so, create text input instead
    if (quizSettings.flashcardMode) {
        createFlashcardTextInput(question);
        return;
    }

    // Regular Radio/Checkbox Options
    const isMultipleAnswer = Array.isArray(question.answer);
    const shuffledOptions = shuffleArray([...(question.options || [])]);

    shuffledOptions.forEach((option, index) => {
      const label = document.createElement('label');
      const input = document.createElement('input');
      input.type = isMultipleAnswer ? 'checkbox' : 'radio';
      input.name = 'option'; // Group radio buttons
      input.value = option;
      input.id = `option${index}`;

      const span = document.createElement('span');
      span.textContent = option;

      label.appendChild(input);
      label.appendChild(span);
      optionsContainerElement.appendChild(label);
    });
}

// Creates text input for flashcard mode
function createFlashcardTextInput(question) {
    if (!optionsContainerElement) return;

    // Remove grid layout and add flashcard-specific class
    optionsContainerElement.classList.remove('options');
    optionsContainerElement.classList.add('flashcard-text-input-container');

    // Create the text input element
    const inputContainer = document.createElement('div');
    inputContainer.className = 'flashcard-input-wrapper';

    const textInput = document.createElement('input');
    textInput.type = 'text';
    textInput.id = 'flashcardTextInput';
    textInput.className = 'flashcard-text-input';
    textInput.placeholder = 'Type your answer here...';
    textInput.autocomplete = 'off';
    textInput.spellcheck = false;

    // Add accessibility attributes
    textInput.setAttribute('aria-label', 'Your answer');
    textInput.setAttribute('aria-describedby', 'flashcard-instructions');

    // Create instructions
    const instructions = document.createElement('div');
    instructions.id = 'flashcard-instructions';
    instructions.className = 'flashcard-instructions';
    instructions.textContent = 'Type your answer and click the check button to see if you\'re correct.';

    // Add event listeners
    textInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            if (checkAnswerBtnElement && !checkAnswerBtnElement.disabled) {
                checkAnswer();
            }
        }
    });

    // Focus the input when created (but only if not in exam mode)
    if (!quizSettings.examMode) {
        setTimeout(() => textInput.focus(), 100);
    }

    inputContainer.appendChild(instructions);
    inputContainer.appendChild(textInput);
    optionsContainerElement.appendChild(inputContainer);
}

// --- Drag and Drop Logic ---
let draggedItem = null; // Holds the element being dragged

function handleDragStart(e) {
    console.log("Drag start event triggered on:", e.target.textContent);
    draggedItem = e.target; // The draggable control div
    e.dataTransfer.effectAllowed = 'move';

    // Make sure dataset.control exists before using it
    if (e.target.dataset && e.target.dataset.control) {
        e.dataTransfer.setData('text/plain', e.target.dataset.control); // Store the control text
        console.log("Drag data set:", e.target.dataset.control);
    } else {
        // Fallback to using the text content if dataset.control is not available
        e.dataTransfer.setData('text/plain', e.target.textContent);
        console.log("Drag data set (fallback to textContent):", e.target.textContent);
    }

    // Add styling to indicate dragging
    setTimeout(() => {
        // Check if draggedItem still exists before adding class
        if (draggedItem) {
            draggedItem.classList.add('dragging');
            console.log("Added dragging class to element");
        }
    }, 0);
}


function handleDragEnd(e) {
    // Remove dragging styles
    if (draggedItem) { // Check if drag was successful
        draggedItem.classList.remove('dragging');
    }
    draggedItem = null;
}

function handleDragOver(e) {
    e.preventDefault(); // Necessary to allow dropping

    // Make sure the target is a valid drop zone
    if (e.target.classList.contains('drop-zone') ||
        e.target.closest('.drop-zone') ||
        e.target.classList.contains('location-box') ||
        e.target.closest('.location-box') ||
        e.target.classList.contains('category-box') ||
        e.target.closest('.category-box') ||
        e.target.classList.contains('ordering-slot') ||
        e.target.closest('.ordering-slot')) {
        e.dataTransfer.dropEffect = 'move';

        // Add visual feedback for valid drop target
        const dropTarget = e.target.classList.contains('drop-zone') ?
            e.target :
            (e.target.closest('.drop-zone') ||
             e.target.closest('.location-box') ||
             e.target.closest('.category-box') ||
             e.target.closest('.ordering-slot'));

        if (dropTarget) {
            dropTarget.classList.add('drag-over');
        }
    } else {
        e.dataTransfer.dropEffect = 'none'; // Not a valid drop target
    }
}

function handleDragEnter(e) {
    e.preventDefault();
    const target = e.target;

    // Handle case where target is a child element of a drop zone
    if (target.parentElement && target.parentElement.classList.contains('drop-zone')) {
        target.parentElement.classList.add('over');
        return;
    }

    // Highlight potential drop target when item enters it
    if (target.classList.contains('drop-zone') && !target.hasChildNodes()) {
        target.classList.add('over');
    } else if (target.id === 'draggableControlsContainer' && draggedItem && draggedItem.parentElement && draggedItem.parentElement.classList.contains('drop-zone')) {
        // Highlight the source list if dragging from a drop zone back to the list
        target.classList.add('over');
    } else if (target.classList.contains('drop-zone') && target.hasChildNodes() && target.firstChild !== draggedItem) {
        // Highlight filled drop zone if trying to swap (for single-item zones)
        // For multi-item zones (like categories), always allow highlighting
        if (!target.classList.contains('category-item-drop-zone') && !target.classList.contains('ordering-drop-zone')) { // Check specific classes
             target.classList.add('over');
        } else {
             target.classList.add('over'); // Always highlight category/ordering item zones
        }
    } else if (target.classList.contains('category-item-drop-zone') || target.classList.contains('ordering-drop-zone')) { // Check specific classes
         target.classList.add('over'); // Highlight empty category/ordering item zone
    } else if (target.closest('#draggableControlsContainer')) {
        // Handle case where target is a child element of the draggable controls container
        const container = document.getElementById('draggableControlsContainer');
        if (container && draggedItem && draggedItem.parentElement && draggedItem.parentElement.classList.contains('drop-zone')) {
            container.classList.add('over');
        }
    }
}


function handleDragLeave(e) {
    // Remove highlight when item leaves drop target
    const target = e.target;

    // Handle case where target is a child element of a drop zone
    if (target.parentElement && target.parentElement.classList.contains('drop-zone')) {
        // Don't remove the 'over' class if the mouse is still within the drop zone
        // This prevents flickering when moving over child elements
        const rect = target.parentElement.getBoundingClientRect();
        const x = e.clientX;
        const y = e.clientY;

        // Check if the mouse is still within the drop zone boundaries
        if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
            target.parentElement.classList.remove('over');
            target.parentElement.classList.remove('drag-over');
        }
        return;
    }

    if (target.classList.contains('drop-zone')) {
        target.classList.remove('over');
        target.classList.remove('drag-over');
    } else if (target.id === 'draggableControlsContainer') {
        target.classList.remove('over');
        target.classList.remove('drag-over');
    } else if (target.closest('#draggableControlsContainer')) {
        // Handle case where target is a child element of the draggable controls container
        const container = document.getElementById('draggableControlsContainer');
        if (container) {
            // Only remove if the mouse has actually left the container
            const rect = container.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;

            if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
                container.classList.remove('over');
                container.classList.remove('drag-over');
            }
        }
    }

    // Also check for other drop zone types
    const dropTarget = target.closest('.location-box') ||
                       target.closest('.category-box') ||
                       target.closest('.ordering-slot');

    if (dropTarget) {
        // Only remove if the mouse has actually left the container
        const rect = dropTarget.getBoundingClientRect();
        const x = e.clientX;
        const y = e.clientY;

        if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
            dropTarget.classList.remove('over');
            dropTarget.classList.remove('drag-over');
        }
    }
}

function handleDrop(e) {
    e.preventDefault();
    const target = e.target;

    console.log("Drop event triggered on:", target);
    console.log("Drop target classes:", target.className);
    console.log("Drop target dataset:", JSON.stringify(target.dataset));

    // Remove hover effect from all drop zones
    document.querySelectorAll('.drop-zone.over, .drop-zone.drag-over').forEach(zone => {
        zone.classList.remove('over');
        zone.classList.remove('drag-over');
    });

    if (!draggedItem) {
        console.error("No dragged item found in handleDrop");
        return; // Exit if no item is being dragged
    }

    console.log("Dragged item:", draggedItem.textContent);
    console.log("Dragged item dataset:", JSON.stringify(draggedItem.dataset));

    // Get the source container (where the item came from)
    const sourceContainer = draggedItem.parentElement;

    // Find the actual drop target (could be the target or a parent element)
    let dropTarget = null;

    // Case 1: Direct drop on a drop zone
    if (target.classList.contains('drop-zone')) {
        dropTarget = target;
    }
    // Case 2: Drop on an element inside a drop zone
    else if (target.closest('.drop-zone')) {
        dropTarget = target.closest('.drop-zone');
    }
    // Case 3: Drop on the controls container
    else if (target.id === 'draggableControlsContainer' || target.closest('#draggableControlsContainer')) {
        // Handle returning to the controls list
        if (sourceContainer && sourceContainer.classList.contains('drop-zone')) {
            // Remove from source drop zone
            sourceContainer.removeChild(draggedItem);
            sourceContainer.classList.remove('filled');

            // Add back to controls container
            const controlsContainer = document.getElementById('draggableControlsContainer');
            if (controlsContainer) {
                controlsContainer.appendChild(draggedItem);
                console.log("Item returned to controls list");
            }
        }
    }

    // If we found a valid drop zone target, handle the drop
    if (dropTarget) {
        console.log("Valid drop target found:", dropTarget);

        // Check if the drop zone already has an item
        const existingItem = dropTarget.querySelector('.draggable-control');
        if (existingItem) {
            // Move existing item back to the controls container
            const controlsContainer = document.getElementById('draggableControlsContainer');
            if (controlsContainer) {
                controlsContainer.appendChild(existingItem);
                console.log("Existing item moved back to controls list");
            }
        }

        // If the dragged item is coming from another drop zone, clear that zone
        if (sourceContainer && sourceContainer.classList.contains('drop-zone')) {
            sourceContainer.removeChild(draggedItem);
            sourceContainer.classList.remove('filled');
        } else if (sourceContainer) {
            // If coming from the controls list, remove it from there
            sourceContainer.removeChild(draggedItem);
        }

        // Add the dragged item to the drop zone
        dropTarget.appendChild(draggedItem);
        dropTarget.classList.add('filled');
        console.log("Item added to drop zone:", dropTarget);
    }

    // Reset dragging styles
    draggedItem.classList.remove('dragging');

    // Clear the draggedItem reference
    draggedItem = null;

    // Log the current state for debugging
    console.log("Drop completed. Drop zones:", document.querySelectorAll('.drop-zone').length);
    document.querySelectorAll('.drop-zone').forEach((zone, index) => {
        console.log(`Zone ${index}:`, zone.innerHTML ? "Filled" : "Empty");
    });
}

// Note: These helper functions are no longer used as their functionality
// has been integrated directly into the handleDrop function for better reliability


// Creates the HTML for the matching question type (Drag and Drop)
function createMatchingHTML(question) {
    if (!optionsContainerElement) return;
    optionsContainerElement.innerHTML = ''; // Clear previous content
    optionsContainerElement.classList.remove('options', 'ordering-container'); // Remove other layout classes
    optionsContainerElement.classList.add('matching-options', 'matching-dnd-container'); // Add specific classes

    // 1. Create container for available controls (Source List)
    const controlsListContainer = document.createElement('div');
    controlsListContainer.id = 'draggableControlsContainer';
    controlsListContainer.classList.add('controls-list-container');
    // Add drop listeners to allow returning items
    controlsListContainer.addEventListener('dragover', handleDragOver);
    controlsListContainer.addEventListener('dragenter', handleDragEnter);
    controlsListContainer.addEventListener('dragleave', handleDragLeave);
    controlsListContainer.addEventListener('drop', handleDrop);

    const controlsTitle = document.createElement('h4');
    controlsTitle.textContent = 'Available Security Controls:';
    controlsListContainer.appendChild(controlsTitle);

    (question.availableControls || []).forEach(control => {
        const controlEl = document.createElement('div');
        controlEl.textContent = control;
        controlEl.classList.add('draggable-control');
        controlEl.draggable = true;
        controlEl.dataset.control = control; // Store control value
        // Create a unique, valid ID for the element
        controlEl.id = `control-${control.replace(/[^a-zA-Z0-9-_]/g, '_')}`;
        controlEl.addEventListener('dragstart', handleDragStart);
        controlEl.addEventListener('dragend', handleDragEnd);
        controlsListContainer.appendChild(controlEl);
    });

    // 2. Create container for locations and drop zones
    const locationsContainer = document.createElement('div');
    locationsContainer.classList.add('locations-container');

    const shuffledLocations = shuffleArray([...(question.locations || [])]); // Shuffle locations

    shuffledLocations.forEach(location => {
        const locationDiv = document.createElement('div');
        locationDiv.classList.add('matching-location');
        locationDiv.dataset.locationId = location.id; // Add data attribute for easy selection

        // Location Info
        const locationInfoDiv = document.createElement('div');
        locationInfoDiv.classList.add('matching-location-info');
        if (location.icon) {
            const iconEl = document.createElement('i');
            // Use the class name directly from the JSON (e.g., "fa-solid fa-building")
            iconEl.className = `${location.icon} matching-icon`; // Use Font Awesome class + existing style class
            locationInfoDiv.appendChild(iconEl);
        }
        const nameEl = document.createElement('strong');
        nameEl.textContent = location.name;
        locationInfoDiv.appendChild(nameEl);
        // Only add description if it exists
        if (location.description) {
            const descEl = document.createElement('span');
            descEl.textContent = ` (${location.description})`;
            locationInfoDiv.appendChild(descEl);
        }

        // Drop Zones Container
        const dropZonesDiv = document.createElement('div');
        dropZonesDiv.classList.add('drop-zones');

        const correctAnswersForLocation = question.correctMatches[location.id] || [];
        const numberOfDropZones = correctAnswersForLocation.length;

        for (let i = 0; i < numberOfDropZones; i++) {
            const dropZone = document.createElement('div');
            dropZone.classList.add('drop-zone'); // Single-item drop zone
            dropZone.dataset.locationId = location.id;
            dropZone.dataset.index = i; // Differentiate zones for the same location
            // Add drop event listeners
            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('dragenter', handleDragEnter);
            dropZone.addEventListener('dragleave', handleDragLeave);
            dropZone.addEventListener('drop', handleDrop);
            dropZonesDiv.appendChild(dropZone);
        }

        locationDiv.appendChild(locationInfoDiv);
        locationDiv.appendChild(dropZonesDiv);
        locationsContainer.appendChild(locationDiv);
    });

    // Add both containers to the main options container
    optionsContainerElement.appendChild(controlsListContainer);
    optionsContainerElement.appendChild(locationsContainer);
}

// Creates the HTML for the matching_categories question type (Drag Scenarios to Categories)
function createMatchingCategoriesHTML(question) {
    if (!optionsContainerElement) return;
    optionsContainerElement.innerHTML = ''; // Clear previous content
    optionsContainerElement.classList.remove('options', 'ordering-container'); // Remove other layout classes
    optionsContainerElement.classList.add('matching-options', 'matching-dnd-container'); // Add specific classes

    // 1. Create container for draggable scenarios (Source List)
    const scenariosListContainer = document.createElement('div');
    scenariosListContainer.id = 'draggableControlsContainer'; // Reusing ID for consistency
    scenariosListContainer.classList.add('controls-list-container', 'scenarios-list'); // Add specific class
    // Add drop listeners to allow returning items
    scenariosListContainer.addEventListener('dragover', handleDragOver);
    scenariosListContainer.addEventListener('dragenter', handleDragEnter);
    scenariosListContainer.addEventListener('dragleave', handleDragLeave);
    scenariosListContainer.addEventListener('drop', handleDrop);

    const scenariosTitle = document.createElement('h4');
    scenariosTitle.textContent = 'Scenarios:';
    scenariosListContainer.appendChild(scenariosTitle);

    const shuffledScenarios = shuffleArray([...(question.scenarios || [])]);

    shuffledScenarios.forEach(scenario => {
        const scenarioEl = document.createElement('div');
        scenarioEl.textContent = scenario.text;
        scenarioEl.classList.add('draggable-control', 'scenario-item'); // Add specific class
        scenarioEl.draggable = true;
        scenarioEl.dataset.control = scenario.id; // Store scenario ID as the 'control' identifier
        scenarioEl.id = `control-${scenario.id.replace(/[^a-zA-Z0-9-_]/g, '_')}`;
        scenarioEl.addEventListener('dragstart', handleDragStart);
        scenarioEl.addEventListener('dragend', handleDragEnd);
        scenariosListContainer.appendChild(scenarioEl);
    });

    // 2. Create container for category drop zones
    const categoriesContainer = document.createElement('div');
    categoriesContainer.classList.add('locations-container', 'categories-container'); // Reuse class, add specific

    // Use attackTypes for drop zones if categories is missing/empty, otherwise use categories
    let dropZoneLabels = (question.categories && question.categories.length > 0) ? question.categories : (question.attackTypes || []);
    dropZoneLabels = shuffleArray([...dropZoneLabels]); // Shuffle categories/attackTypes
    const labelKey = (question.categories && question.categories.length > 0) ? 'categoryId' : 'attackTypeId'; // Use different dataset key if needed

    dropZoneLabels.forEach(labelName => {
        const categoryDiv = document.createElement('div');
        categoryDiv.classList.add('matching-location', 'category-drop-area'); // Reuse class, add specific
        categoryDiv.dataset[labelKey] = labelName; // Store the label (category or attack type)

        const categoryInfoDiv = document.createElement('div');
        categoryInfoDiv.classList.add('matching-location-info', 'category-title');
        const nameEl = document.createElement('strong');
        nameEl.textContent = labelName; // Use the dynamic label name
        categoryInfoDiv.appendChild(nameEl);

        // Drop Zones Container for this category/label
        const dropZonesDiv = document.createElement('div');
        dropZonesDiv.classList.add('drop-zones', 'category-drop-zones-container'); // Add specific class

        // Determine max number of items for any category (or use a fixed number like 3)
        let maxItemsPerCategory = 0;
        if (question.correctMatches) {
            const categoryCounts = {};
            Object.values(question.correctMatches).forEach(cat => {
                categoryCounts[cat] = (categoryCounts[cat] || 0) + 1;
            });
            maxItemsPerCategory = Math.max(...Object.values(categoryCounts), 0);
        }
        const numberOfDropZones = Math.max(3, maxItemsPerCategory); // Ensure at least 3 zones

        for (let i = 0; i < numberOfDropZones; i++) {
            const dropZone = document.createElement('div');
            dropZone.classList.add('drop-zone', 'category-item-drop-zone'); // Specific class for individual zones
            dropZone.dataset.locationId = labelName; // Associate with the label (category or attack type)
            dropZone.dataset.index = i; // Differentiate zones
            // Add drop event listeners
            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('dragenter', handleDragEnter);
            dropZone.addEventListener('dragleave', handleDragLeave);
            dropZone.addEventListener('drop', handleDrop); // Use the standard drop handler
            dropZonesDiv.appendChild(dropZone);
        }

        categoryDiv.appendChild(categoryInfoDiv);
        categoryDiv.appendChild(dropZonesDiv); // Add the container of zones
        categoriesContainer.appendChild(categoryDiv);
    });

    // Add both containers to the main options container
    optionsContainerElement.appendChild(categoriesContainer); // Categories first visually
    optionsContainerElement.appendChild(scenariosListContainer); // Scenarios below
}

// Creates the HTML for the ordering question type (Drag and Drop)
function createOrderingHTML(question) {
    if (!optionsContainerElement) return;
    optionsContainerElement.innerHTML = ''; // Clear previous content
    optionsContainerElement.classList.remove('options'); // Remove grid layout class
    optionsContainerElement.classList.add('matching-options', 'matching-dnd-container', 'ordering-container'); // Add specific classes

    // 1. Create container for available items (Source List)
    const itemsListContainer = document.createElement('div');
    itemsListContainer.id = 'draggableControlsContainer'; // Reusing ID
    itemsListContainer.classList.add('controls-list-container', 'ordering-source-list');
    // Add drop listeners to allow returning items
    itemsListContainer.addEventListener('dragover', handleDragOver);
    itemsListContainer.addEventListener('dragenter', handleDragEnter);
    itemsListContainer.addEventListener('dragleave', handleDragLeave);
    itemsListContainer.addEventListener('drop', handleDrop);

    const itemsTitle = document.createElement('h4');
    itemsTitle.textContent = 'Items to Order:';
    itemsListContainer.appendChild(itemsTitle);

    const shuffledItems = shuffleArray([...(question.items || [])]); // Shuffle the source items

    shuffledItems.forEach(itemText => {
        const itemEl = document.createElement('div');
        itemEl.textContent = itemText;
        itemEl.classList.add('draggable-control', 'ordering-item');
        itemEl.draggable = true;
        itemEl.dataset.control = itemText; // Store item text
        itemEl.id = `control-${itemText.replace(/[^a-zA-Z0-9-_]/g, '_')}`;
        itemEl.addEventListener('dragstart', handleDragStart);
        itemEl.addEventListener('dragend', handleDragEnd);
        itemsListContainer.appendChild(itemEl);
    });

    // 2. Create container for the ordered drop zones (Target List)
    const dropZoneContainer = document.createElement('div');
    dropZoneContainer.id = 'orderingDropZoneContainer';
    dropZoneContainer.classList.add('locations-container', 'ordering-target-list'); // Reuse class, add specific

    const dropZoneTitle = document.createElement('h4');
    dropZoneTitle.textContent = 'Correct Order:';
    dropZoneContainer.appendChild(dropZoneTitle);

    const numberOfDropZones = question.items?.length || 0;

    for (let i = 0; i < numberOfDropZones; i++) {
        const dropZoneDiv = document.createElement('div');
        dropZoneDiv.classList.add('ordering-drop-zone-wrapper');

        const numberSpan = document.createElement('span');
        numberSpan.classList.add('ordering-number');
        numberSpan.textContent = `${i + 1}.`;

        const dropZone = document.createElement('div');
        dropZone.classList.add('drop-zone', 'ordering-drop-zone'); // Specific class for ordering drop zones
        dropZone.dataset.index = i; // Store the intended index
        // Add drop event listeners
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragenter', handleDragEnter);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);

        dropZoneDiv.appendChild(numberSpan);
        dropZoneDiv.appendChild(dropZone);
        dropZoneContainer.appendChild(dropZoneDiv);
    }

    // Add both containers to the main options container
    optionsContainerElement.appendChild(itemsListContainer); // Source list on the left/top
    optionsContainerElement.appendChild(dropZoneContainer); // Target list on the right/bottom
}


// Display current question (Refactored to update elements directly)
function displayQuestion() {
  console.log("Entering displayQuestion. Current mode:", currentMode, "Index:", currentQuestionIndex);

  // Handle end of quiz
  if (currentQuestionIndex >= currentQuestions.length) {
    console.log("End of questions reached.");
    if (currentMode === 'select') { // Only 'select' mode uses the single question finish
      finishSingleQuestionQuiz();
    } else { // All other modes (including 'acronyms') use the standard finish
      finishQuiz();
    }
    return;
  }

  const question = currentQuestions[currentQuestionIndex];
  if (!question) {
     console.error("Invalid question object at index:", currentQuestionIndex, "Data:", currentQuestions);
     nextQuestion(); // Try to skip invalid question
     return;
  }
  console.log("Processing question:", JSON.parse(JSON.stringify(question))); // Log the question data
  questionStartTime = Date.now();

  // --- Start DOM Update Logging & Try/Catch ---
  try {
      console.log("Checking DOM elements before update...");
      console.log("questionNumberElement exists:", !!questionNumberElement);
      console.log("progressBar exists:", !!progressBar);
      console.log("questionTextElement exists:", !!questionTextElement);
      console.log("optionsContainerElement exists:", !!optionsContainerElement);
      console.log("categoryTagElement exists:", !!categoryTagElement);
      console.log("difficultyTagElement exists:", !!difficultyTagElement);
      console.log("resultContainerElement exists:", !!resultContainerElement);
      console.log("favoriteBtnElement exists:", !!favoriteBtnElement);
      console.log("hintContainerElement exists:", !!hintContainerElement);
      console.log("checkAnswerBtnElement exists:", !!checkAnswerBtnElement);
      console.log("nextQuestionBtnElement exists:", !!nextQuestionBtnElement);
      console.log("timerContainerElement exists:", !!timerContainerElement);

      // Update Progress Bar & Question Number (standardized across all modes)
      if (questionNumberElement) {
        // Standardize display format across all modes
        questionNumberElement.textContent = `Question ${currentQuestionIndex + 1}/${currentQuestions.length}`;
      }

      // Update progress bar with animation
      if (progressBar) {
        // Remove any existing transition before setting new width
        progressBar.style.transition = 'none';
        progressBar.offsetHeight; // Force reflow

        // Add transition back and set new width
        progressBar.style.transition = 'width 0.5s ease-in-out';
        const progressPercentage = ((currentQuestionIndex + 1) / currentQuestions.length) * 100;
        progressBar.style.width = `${progressPercentage}%`;

        // Change color based on progress
        if (progressPercentage < 30) {
          progressBar.style.backgroundColor = 'var(--warning-color)';
        } else if (progressPercentage < 70) {
          progressBar.style.backgroundColor = 'var(--primary-color)';
        } else {
          progressBar.style.backgroundColor = 'var(--success-color)';
        }
      }

      // Update weekly progress bar
      updateWeeklyProgressBar();

      // Update Question Text (Use questionText for matching/ordering types)
      const isMultipleAnswer = question['multiple answers'] && question['multiple answers'] > 1;
      let questionDisplayKey = (question.type === 'matching' || question.type === 'matching_categories' || question.type === 'ordering') ? 'questionText' : 'question';
      let questionHtml = formatQuestionText(question[questionDisplayKey]);

      if (isMultipleAnswer) {
        questionHtml += `<br><small>(Select ${question['multiple answers']} correct answers)</small>`;
      }
      if (questionTextElement) {
        console.log("Updating question text element...");
        questionTextElement.innerHTML = questionHtml;
      } else {
        console.error("Cannot update question text: questionTextElement is null.");
      }

      // Update Category & Difficulty Tags
      const categoryTagsContainer = document.getElementById('categoryTags');
      if (categoryTagsContainer) {
          categoryTagsContainer.classList.remove('hidden'); // Ensure visible
      }
      if (categoryTagElement) {
            // --- Updated Category Logic ---
            let categoryText = 'N/A'; // Default
            if (currentMode === 'ai-learning') {
                categoryText = question.category || 'AI Generated'; // Use AI category or default
            } else if (question.sectionTitle) {
                categoryText = question.sectionTitle; // Use section title if available
            } else if (question.category) {
                categoryText = `Section ${question.category}`; // Format non-AI category
            }
            console.log("Updating category tag element with:", categoryText);
            categoryTagElement.textContent = categoryText;
            // --- End Updated Category Logic ---
          } else {
             console.error("Cannot update category tag: categoryTagElement is null.");
          }
      if (difficultyTagElement) {
            const originalDifficulty = question.difficulty || 'medium';
            console.log("Updating difficulty tag element with:", originalDifficulty);
            if (originalDifficulty && originalDifficulty !== 'medium') {
              const difficultyClass = originalDifficulty.replace(' ', '-');
              difficultyTagElement.className = `difficulty-badge difficulty-${difficultyClass}`;
              difficultyTagElement.textContent = capitalizeFirstLetter(originalDifficulty);
              difficultyTagElement.classList.remove('hidden');
            } else {
              difficultyTagElement.classList.add('hidden');
            }
          } else {
             console.error("Cannot update difficulty tag: difficultyTagElement is null.");
          }

      // Create and display options OR matching/ordering interface based on type
      console.log("Checking question type for options/interface creation:", question.type);
      if (question.type === 'matching') {
        console.log("Creating matching HTML...");
        createMatchingHTML(question);
      } else if (question.type === 'matching_categories') {
        console.log("Creating matching categories HTML...");
        createMatchingCategoriesHTML(question); // Call the new function
      } else if (question.type === 'ordering') {
        console.log("Creating ordering HTML...");
        createOrderingHTML(question); // Add call for ordering type
      } else {
        console.log("Creating standard options HTML...");
        createOptionsHTML(question); // Fallback for radio/checkbox/acronyms/ai
      }

      // Clear previous results
      if (resultContainerElement) {
           console.log("Clearing result container...");
           resultContainerElement.innerHTML = '';
      } else {
          console.error("Cannot clear results: resultContainerElement is null.");
      }

      // Update favorite button state
      const favoriteContainer = document.querySelector('.question-favorites');
      if (favoriteContainer) {
          favoriteContainer.classList.remove('hidden'); // Ensure visible
      }
      if (favoriteBtnElement) {
          const isFav = isFavorite(question.id);
          console.log("Updating favorite button state. Is favorite:", isFav);
          favoriteBtnElement.classList.toggle('active', isFav);
      } else {
          console.error("Cannot update favorite button: favoriteBtnElement is null.");
      }

      // Handle hint visibility and content (Force hide for now)
      if (hintContainerElement) {
          hintContainerElement.classList.add('hidden'); // Ensure hidden
      } else {
          console.error("Cannot hide hint: hintContainerElement is null.");
      }
      // Removed the logic that conditionally showed hint content/button text

      // Set button states (disable next button in select mode)
      if (checkAnswerBtnElement) {
          console.log("Enabling check answer button...");
          checkAnswerBtnElement.disabled = false;
      } else {
           console.error("Cannot enable check answer button: checkAnswerBtnElement is null.");
      }
      if (nextQuestionBtnElement) {
          const disableNext = (currentMode === 'select');
          console.log("Setting next question button disabled state to:", disableNext);
          nextQuestionBtnElement.disabled = disableNext;
      } else {
          console.error("Cannot set next question button state: nextQuestionBtnElement is null.");
      }

      // Add "Back to List" button for select mode
      if (currentMode === 'select') {
        console.log("Adding back to list button for select mode...");
        addBackToListButton();
      }
      // Handle timer visibility (don't show timer for select mode)
      if (timerContainerElement) {
          const hideTimer = (currentMode === 'select');
          console.log("Setting timer visibility. Hidden:", hideTimer);
          timerContainerElement.classList.toggle('hidden', hideTimer);
      } else {
          console.error("Cannot set timer visibility: timerContainerElement is null.");
      }

      // Reset flashcard mode state for the new question if applicable
      if (currentMode === 'flashcards') {
        quizSettings.flashcardMode = true; // Ensure it's true for each item
      }

      // Handle flashcard mode display
      if (optionsContainerElement && checkAnswerBtnElement) {
          if (quizSettings.flashcardMode) {
              console.log("Setting up flashcard mode display...");
              optionsContainerElement.classList.add('hidden');
              checkAnswerBtnElement.innerHTML = '<i class="fa-solid fa-eye"></i>'; // Use icon
          } else {
              optionsContainerElement.classList.remove('hidden'); // Explicitly show options if not flashcard mode
              checkAnswerBtnElement.innerHTML = '<i class="fa-solid fa-eye"></i>'; // Use icon
          }
      }
      console.log("displayQuestion finished updates.");
  } catch (error) {
      console.error("Error occurred within displayQuestion DOM updates:", error);
      // Optionally, show a user-friendly error message on the screen
      if (questionTextElement) {
          questionTextElement.textContent = "An error occurred displaying the question. Please check the console.";
      }
      if (optionsContainerElement) {
          optionsContainerElement.innerHTML = "";
      }
  }
  // --- End DOM Update Logging & Try/Catch ---
}

// --- Timers ---

// Timer functions (Refactored to use cached elements)
function startTimer(seconds, type) {
  clearInterval(timerInterval);
  clearInterval(examTimerInterval);

  if (!timeInfoElement) {
     console.error("Time info element not cached or found");
     return;
  }

  if (type === 'exam') {
    examTimeRemaining = seconds;
    updateExamTimerDisplay(); // Initial display
    examTimerInterval = setInterval(() => {
      examTimeRemaining--;
      updateExamTimerDisplay();
      if (examTimeRemaining <= 0) {
        clearInterval(examTimerInterval);
        finishQuiz(); // End quiz when exam time runs out
      }
      if (examTimeRemaining === 60) showFeedback('One minute remaining!');
      else if (examTimeRemaining === 30) showFeedback('30 seconds remaining!');
    }, 1000);
  } else if (type === 'question') {
     let currentSeconds = seconds;
     if (timerDisplayElement) timerDisplayElement.textContent = currentSeconds;
     if (timeInfoElement) timeInfoElement.textContent = `${currentSeconds}s remaining`;

     timerInterval = setInterval(() => {
       currentSeconds--;
       if (timerDisplayElement) timerDisplayElement.textContent = currentSeconds;
       if (timeInfoElement) timeInfoElement.textContent = `${currentSeconds}s remaining`;

       if (timerDisplayElement) {
          timerDisplayElement.style.color = currentSeconds <= 5 ? 'var(--danger-color)' : ''; // Use CSS var
       }

       if (currentSeconds <= 0) {
         clearInterval(timerInterval);
         handleTimeUp(); // Handle time up for the specific question
       }
     }, 1000);
  } else if (type === 'quiz' && !quizSettings.examMode) { // General quiz timer (non-exam, if ever needed)
     let currentSeconds = seconds;
     updateQuizTimerDisplay(currentSeconds);
     timerInterval = setInterval(() => {
       currentSeconds--;
       updateQuizTimerDisplay(currentSeconds);
       if (timeInfoElement && currentSeconds <= 30) timeInfoElement.style.color = 'var(--danger-color)';
       if (currentSeconds <= 0) {
         clearInterval(timerInterval);
         finishQuiz();
       }
     }, 1000);
  }
}

function updateExamTimerDisplay() {
   if (!timeInfoElement) return;
   const minutes = Math.floor(examTimeRemaining / 60);
   const remainingSeconds = examTimeRemaining % 60;
   timeInfoElement.textContent = `Exam time: ${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
   timeInfoElement.style.color = examTimeRemaining <= 30 ? 'var(--danger-color)' : '';
}

function updateQuizTimerDisplay(seconds) {
   if (!timeInfoElement) return;
   const minutes = Math.floor(seconds / 60);
   const remainingSeconds = seconds % 60;
   timeInfoElement.textContent = `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds} remaining`;
}

// Handle when time is up for a question (non-exam mode)
function handleTimeUp() {
  if (checkAnswerBtnElement) checkAnswerBtnElement.disabled = true;
  // Only enable next button if not in select mode
  if (nextQuestionBtnElement && currentMode !== 'select') {
      nextQuestionBtnElement.disabled = false;
  }

  if (resultContainerElement) {
    resultContainerElement.innerHTML = '<div class="feedback" style="color: var(--danger-color);">Time\'s up!</div>';

    const question = currentQuestions[currentQuestionIndex];
    if (question) {
       // For matching/ordering types, show correct answers differently
       if (question.type === 'matching' || question.type === 'matching_categories' || question.type === 'ordering') {
           resultContainerElement.innerHTML += `<p>Time ran out. Check the correct answer.</p>`;
           // Disable dragging and highlight correct/incorrect after timeout
           const draggables = optionsContainerElement?.querySelectorAll('.draggable-control');
           draggables?.forEach(el => el.draggable = false);
           displayAnswerFeedback(question, null, getSelectedAnswer(), false); // Show feedback even on timeout
       } else {
           const isMultipleAnswer = Array.isArray(question.answer);
           const correctAnswerText = isMultipleAnswer ? question.answer.join(', ') : question.answer;
           resultContainerElement.innerHTML += `<p>The correct answer${isMultipleAnswer ? 's were' : ' was'}: ${correctAnswerText}</p>`;
           const options = optionsContainerElement?.querySelectorAll('input');
           options?.forEach(option => {
             option.disabled = true;
             const isCorrectOption = isMultipleAnswer ? question.answer.includes(option.value) : option.value === question.answer;
             if (isCorrectOption) {
               highlightOption(option, 'var(--success-color)');
             }
           });
       }

       // Only save result if not in 'select' mode
       if (currentMode !== 'select') {
         saveQuestionResult(question.id, false, 'timeout');
       }
       currentQuizAnswers[currentQuestionIndex] = { userAnswer: 'Time\'s up - No answer', isCorrect: false };

       // Enable back button in select mode
       const backToListBtn = document.getElementById('backToListBtn');
       if (backToListBtn && currentMode === 'select') {
           backToListBtn.disabled = false;
       }
    }
  }
}

// --- Modularized Answer Checking ---

// Main checkAnswer function
function checkAnswer() {
  const question = currentQuestions[currentQuestionIndex];
  if (!question) return;

  // Handle flashcard mode - check if we have text input
  if (quizSettings.flashcardMode) {
    const textInput = document.getElementById('flashcardTextInput');
    if (textInput) {
      // We have text input, so evaluate the answer
      handleFlashcardTextAnswer(question);
      return;
    } else {
      // Old flashcard behavior - just reveal options
      if (optionsContainerElement) optionsContainerElement.classList.remove('hidden');
      if (checkAnswerBtnElement) checkAnswerBtnElement.innerHTML = '<i class="fa-solid fa-eye"></i>'; // Use icon
      quizSettings.flashcardMode = false; // Mark as revealed
      return; // Don't check answer yet, just reveal
    }
  }

  // --- Non-Flashcard Modes ---

  // Handle based on question type first
  if (question.type === 'matching' || question.type === 'matching_categories') {
      // Check if all draggable items *should* have been placed and if any remain
      const remainingDraggables = optionsContainerElement?.querySelectorAll('#draggableControlsContainer .draggable-control');
      const initialDraggablesCount = question.availableControls?.length || question.scenarios?.length || 0; // Get initial count based on type
      const totalDropZones = optionsContainerElement?.querySelectorAll('.drop-zone').length || 0;

      // Only block if all items were *supposed* to be used (initial count == drop zones) AND items remain
      if (initialDraggablesCount === totalDropZones && remainingDraggables && remainingDraggables.length > 0 && !quizSettings.examMode) {
          displayFeedbackMessage('Please match all items!', 'var(--warning-color)');
          return; // Block only if all items were required
      }
      // Otherwise, proceed even if items remain (for questions like A1)
      handleMatchingAnswer(question); // Use the same handler for both matching types
  } else if (question.type === 'ordering') {
      // Check if all drop zones are filled
      const dropZones = optionsContainerElement?.querySelectorAll('.ordering-drop-zone');
      const filledZones = optionsContainerElement?.querySelectorAll('.ordering-drop-zone.filled');
      if (dropZones && filledZones && filledZones.length < dropZones.length && !quizSettings.examMode) {
          displayFeedbackMessage('Please place all items in order!', 'var(--warning-color)');
          return;
      }
      handleOrderingAnswer(question); // Specific handler for ordering
  } else {
      // Handle Radio/Checkbox types
      const selectedOptions = optionsContainerElement ? optionsContainerElement.querySelectorAll('input[name="option"]:checked') : [];
      // Check if an answer was selected (except in exam mode)
      if (selectedOptions.length === 0 && !quizSettings.examMode) {
          displayFeedbackMessage('Please select an answer!', 'var(--danger-color)');
          return;
      }
      // Handle based on quiz mode for non-matching types
      if (quizSettings.examMode) {
          handleExamAnswer(question, selectedOptions);
      } else {
          handleRegularAnswer(question, selectedOptions);
      }
  }
}

// Handles answer logic for matching questions (Locations and Categories)
function handleMatchingAnswer(question) {
    console.log(`Handling ${question.type} answer for question:`, question.id);
    if (checkAnswerBtnElement) checkAnswerBtnElement.disabled = true;
    if (nextQuestionBtnElement && currentMode !== 'select') {
        nextQuestionBtnElement.disabled = false;
    }

    // Disable further dragging
    const draggables = optionsContainerElement?.querySelectorAll('.draggable-control');
    draggables?.forEach(el => el.draggable = false);
    const dropZones = optionsContainerElement?.querySelectorAll('.drop-zone');
    dropZones?.forEach(zone => {
        zone.removeEventListener('dragover', handleDragOver);
        zone.removeEventListener('dragenter', handleDragEnter);
        zone.removeEventListener('dragleave', handleDragLeave);
        zone.removeEventListener('drop', handleDrop);
        zone.classList.remove('over');
    });

    clearInterval(timerInterval); // Stop question timer (if any)

    const userAnswers = getSelectedAnswer();
    const isCorrect = checkCorrectness(question, userAnswers);

    displayAnswerFeedback(question, null, userAnswers, isCorrect);

    // Display explanations if applicable
    // 1. Check for top-level explanation first (for questions like B2)
    if (quizSettings.showExplanation && question.explanation && resultContainerElement) {
        const explanationDiv = document.createElement('div');
        explanationDiv.id = 'explanationBox'; // Reuse ID
        explanationDiv.innerHTML = `<strong>Explanation:</strong><br>${formatExplanation(question.explanation)}`;
        // Avoid adding duplicate if feedback already exists
        if (!resultContainerElement.querySelector('#explanationBox')) {
             resultContainerElement.appendChild(explanationDiv);
        }
    }
    // 2. Then check for location-specific explanations (for questions like A2)
    else if (question.type === 'matching' && question.locations) {
        question.locations.forEach(loc => {
            if (quizSettings.showExplanation && loc.explanation) { // Also check showExplanation setting
                const locationDiv = optionsContainerElement?.querySelector(`.matching-location[data-location-id="${loc.id}"]`);
                if (locationDiv && !locationDiv.querySelector('.location-explanation')) {
                    const explanationEl = document.createElement('div');
                    explanationEl.classList.add('location-explanation');
                    explanationEl.innerHTML = formatExplanation(loc.explanation);
                    locationDiv.appendChild(explanationEl);
                }
            }
        });
    } else if (question.type === 'matching_categories' && question.explanations) {
         // Display category explanations after checking
         Object.entries(question.explanations).forEach(([categoryName, explanationText]) => {
             const categoryDiv = optionsContainerElement?.querySelector(`.category-drop-area[data-category-id="${categoryName}"]`);
             if (categoryDiv && !categoryDiv.querySelector('.location-explanation')) { // Reuse class
                 const explanationEl = document.createElement('div');
                 explanationEl.classList.add('location-explanation'); // Reuse class
                 explanationEl.innerHTML = `<strong>${categoryName}:</strong><br>${formatExplanation(explanationText)}`; // Add category name
                 categoryDiv.appendChild(explanationEl);
             }
         });
    }
    // Note: Top-level explanation handled above now


    if (isCorrect) {
        correctAnswers++;
        if (currentMode !== 'select') createConfetti();
    }

    if (currentMode !== 'select') {
        saveQuestionResult(question.id, isCorrect, Date.now() - questionStartTime);
    }
    currentQuizAnswers[currentQuestionIndex] = { userAnswer: userAnswers, isCorrect: isCorrect };

    const backToListBtn = document.getElementById('backToListBtn');
    if (backToListBtn && currentMode === 'select') {
        backToListBtn.disabled = false;
    }

    // Show AI Features button
    console.log("[handleMatchingAnswer] Attempting to show AI button. Element exists:", !!aiFeaturesBtnElement); // Add log
    if (aiFeaturesBtnElement) {
        aiFeaturesBtnElement.classList.remove('hidden');
        aiFeaturesBtnElement.style.display = 'inline-block'; // Ensure it's visible
        console.log("[handleMatchingAnswer] AI button should now be visible."); // Add log
    } else {
        console.error("[handleMatchingAnswer] Cannot show AI button because aiFeaturesBtnElement is null or undefined.");
        // Try to find the button by ID as a fallback
        const aiButton = document.getElementById('aiFeaturesBtn');
        if (aiButton) {
            aiButton.classList.remove('hidden');
            aiButton.style.display = 'inline-block';
            console.log("[handleMatchingAnswer] Found AI button by ID and made it visible.");
        }
    }
}

// Handles answer logic for ordering questions
function handleOrderingAnswer(question) {
    console.log(`Handling ordering answer for question:`, question.id);
    if (checkAnswerBtnElement) checkAnswerBtnElement.disabled = true;
    if (nextQuestionBtnElement && currentMode !== 'select') {
        nextQuestionBtnElement.disabled = false;
    }

    // Disable further dragging
    const draggables = optionsContainerElement?.querySelectorAll('.draggable-control');
    draggables?.forEach(el => el.draggable = false);
    const dropZones = optionsContainerElement?.querySelectorAll('.drop-zone');
    dropZones?.forEach(zone => {
        zone.removeEventListener('dragover', handleDragOver);
        zone.removeEventListener('dragenter', handleDragEnter);
        zone.removeEventListener('dragleave', handleDragLeave);
        zone.removeEventListener('drop', handleDrop);
        zone.classList.remove('over');
    });

    clearInterval(timerInterval); // Stop question timer (if any)

    const userAnswers = getSelectedAnswer(); // Should return an array of strings in order
    const isCorrect = checkCorrectness(question, userAnswers);

    displayAnswerFeedback(question, null, userAnswers, isCorrect);
    displayExplanation(question); // Display top-level explanation if available

    if (isCorrect) {
        correctAnswers++;
        if (currentMode !== 'select') createConfetti();
    }

    if (currentMode !== 'select') {
        saveQuestionResult(question.id, isCorrect, Date.now() - questionStartTime);
    }
    currentQuizAnswers[currentQuestionIndex] = { userAnswer: userAnswers, isCorrect: isCorrect };

    const backToListBtn = document.getElementById('backToListBtn');
    if (backToListBtn && currentMode === 'select') {
        backToListBtn.disabled = false;
    }

    // Show AI Features button
    console.log("[handleOrderingAnswer] Attempting to show AI button. Element exists:", !!aiFeaturesBtnElement); // Add log
    if (aiFeaturesBtnElement) {
        aiFeaturesBtnElement.classList.remove('hidden');
        aiFeaturesBtnElement.style.display = 'inline-block'; // Ensure it's visible
        console.log("[handleOrderingAnswer] AI button should now be visible."); // Add log
    } else {
        console.error("[handleOrderingAnswer] Cannot show AI button because aiFeaturesBtnElement is null or undefined.");
        // Try to find the button by ID as a fallback
        const aiButton = document.getElementById('aiFeaturesBtn');
        if (aiButton) {
            aiButton.classList.remove('hidden');
            aiButton.style.display = 'inline-block';
            console.log("[handleOrderingAnswer] Found AI button by ID and made it visible.");
        }
    }
}


// Handles answer logic for exam mode (Radio/Checkbox)
function handleExamAnswer(question, selectedOptions) {
  let userAnswer = Array.from(selectedOptions).map(opt => opt.value);
  userAnswer = userAnswer.length > 0 ? userAnswer : 'Not Answered';

  const isCorrect = checkCorrectness(question, userAnswer);
  if (isCorrect) correctAnswers++;

  saveQuestionResult(question.id, isCorrect, Date.now() - questionStartTime);
  currentQuizAnswers[currentQuestionIndex] = { userAnswer: userAnswer, isCorrect: isCorrect };

  nextQuestion(); // Move immediately to the next question in exam mode
}

// Handles answer logic for non-exam modes (Radio/Checkbox, including 'select' mode)
function handleRegularAnswer(question, selectedOptions) {
  if (checkAnswerBtnElement) checkAnswerBtnElement.disabled = true;
  // Enable next button only if NOT in select mode
  if (nextQuestionBtnElement && currentMode !== 'select') {
      nextQuestionBtnElement.disabled = false;
  }

  const allOptionInputs = optionsContainerElement ? optionsContainerElement.querySelectorAll('input') : [];
  allOptionInputs.forEach(option => option.disabled = true);

  clearInterval(timerInterval); // Stop question timer (if any)

  let selectedAnswers = Array.from(selectedOptions).map(opt => opt.value);
  const isCorrect = checkCorrectness(question, selectedAnswers);

  displayAnswerFeedback(question, selectedOptions, selectedAnswers, isCorrect);
  displayExplanation(question); // Display top-level explanation if available

  if (isCorrect) {
      correctAnswers++;
      // Only show confetti if not in select mode (or maybe always?)
      if (currentMode !== 'select') createConfetti();
  }

  // Only save result if not in 'select' mode (or decide if you want to track attempts)
  if (currentMode !== 'select') {
    saveQuestionResult(question.id, isCorrect, Date.now() - questionStartTime);
  }
  currentQuizAnswers[currentQuestionIndex] = { userAnswer: selectedAnswers.length > 0 ? selectedAnswers : 'Not Answered', isCorrect: isCorrect };

  // If in select mode, the "Next Question" button is replaced by "Back to List"
  // which becomes active after checking the answer.
  const backToListBtn = document.getElementById('backToListBtn');
  if (backToListBtn && currentMode === 'select') {
      backToListBtn.disabled = false;
  }

  // Show AI Features button
  console.log("[handleRegularAnswer] Attempting to show AI button. Element exists:", !!aiFeaturesBtnElement); // Add log
  if (aiFeaturesBtnElement) {
      aiFeaturesBtnElement.classList.remove('hidden');
      aiFeaturesBtnElement.style.display = 'inline-block'; // Ensure it's visible
      console.log("[handleRegularAnswer] AI button should now be visible."); // Add log
  } else {
      console.error("[handleRegularAnswer] Cannot show AI button because aiFeaturesBtnElement is null or undefined.");
      // Try to find the button by ID as a fallback
      const aiButton = document.getElementById('aiFeaturesBtn');
      if (aiButton) {
          aiButton.classList.remove('hidden');
          aiButton.style.display = 'inline-block';
          console.log("[handleRegularAnswer] Found AI button by ID and made it visible.");
      }
  }
}

// Handles flashcard text input answer checking
function handleFlashcardTextAnswer(question) {
  if (!window.SimilarityScoring) {
    console.error('Similarity scoring system not loaded');
    displayFeedbackMessage('Error: Similarity scoring system not available', 'var(--danger-color)');
    return;
  }

  const userAnswer = window.SimilarityScoring.getFlashcardTextAnswer();

  if (!userAnswer) {
    displayFeedbackMessage('Please enter an answer before checking!', 'var(--warning-color)');
    return;
  }

  // Get correct answer(s) - handle both single and multiple answers
  let correctAnswers = question.answer;
  if (!Array.isArray(correctAnswers)) {
    correctAnswers = [correctAnswers];
  }

  // Evaluate the answer using similarity scoring
  const evaluation = window.SimilarityScoring.evaluateAnswer(userAnswer, correctAnswers);

  // Disable check button and enable next button
  if (checkAnswerBtnElement) checkAnswerBtnElement.disabled = true;
  if (nextQuestionBtnElement && currentMode !== 'select') {
    nextQuestionBtnElement.disabled = false;
  }

  // Display feedback with similarity information
  displayFlashcardFeedback(evaluation, question);

  // Update statistics and gamification
  const isCorrect = evaluation.isCorrect;
  updateQuizStatistics(question, userAnswer, isCorrect);

  // Show AI Features button
  if (aiFeaturesBtnElement) {
    aiFeaturesBtnElement.classList.remove('hidden');
    aiFeaturesBtnElement.style.display = 'inline-block';
  }

  // Store the answer for quiz completion
  if (currentQuizAnswers) {
    currentQuizAnswers[question.id] = {
      userAnswer: userAnswer,
      correctAnswer: evaluation.bestMatch,
      isCorrect: isCorrect,
      similarity: evaluation.similarity,
      timeSpent: Date.now() - questionStartTime
    };
  }

  // Update correct answers count
  if (isCorrect) {
    correctAnswers++;
  }
}

// Displays detailed feedback for flashcard text answers
function displayFlashcardFeedback(evaluation, question) {
  if (!resultContainerElement) return;

  // Create feedback container
  const feedbackContainer = document.createElement('div');
  feedbackContainer.className = 'flashcard-feedback-container';

  // Main feedback message
  const feedbackMessage = document.createElement('div');
  feedbackMessage.className = `flashcard-feedback ${evaluation.isCorrect ? 'correct' : 'incorrect'}`;
  feedbackMessage.innerHTML = `
    <div class="feedback-icon">${evaluation.isCorrect ? '✓' : '✗'}</div>
    <div class="feedback-text">${evaluation.feedback}</div>
  `;

  // Answer comparison
  const answerComparison = document.createElement('div');
  answerComparison.className = 'answer-comparison';
  answerComparison.innerHTML = `
    <div class="answer-row">
      <span class="answer-label">Your answer:</span>
      <span class="user-answer">${evaluation.userAnswer}</span>
    </div>
    <div class="answer-row">
      <span class="answer-label">Correct answer:</span>
      <span class="correct-answer">${evaluation.bestMatch}</span>
    </div>
  `;

  // Similarity details (only show if not perfect match)
  if (evaluation.similarity < 0.95) {
    const similarityDetails = document.createElement('div');
    similarityDetails.className = 'similarity-details';
    similarityDetails.innerHTML = `
      <div class="similarity-score">
        <span class="similarity-label">Similarity:</span>
        <span class="similarity-value">${Math.round(evaluation.similarity * 100)}%</span>
      </div>
    `;
    answerComparison.appendChild(similarityDetails);
  }

  feedbackContainer.appendChild(feedbackMessage);
  feedbackContainer.appendChild(answerComparison);

  // Show explanation if available
  if (question.explanation) {
    const explanationContainer = document.createElement('div');
    explanationContainer.className = 'flashcard-explanation';
    explanationContainer.innerHTML = `
      <div class="explanation-header">Explanation:</div>
      <div class="explanation-content">${question.explanation}</div>
    `;
    feedbackContainer.appendChild(explanationContainer);
  }

  // Clear previous results and add new feedback
  resultContainerElement.innerHTML = '';
  resultContainerElement.appendChild(feedbackContainer);
  resultContainerElement.classList.remove('hidden');
}

// Checks if the selected answer(s) are correct
function checkCorrectness(question, selectedAnswers) {
    console.log("Checking correctness for question type:", question.type);
    console.log("Question ID:", question.id);
    console.log("Selected answers:", JSON.stringify(selectedAnswers));

    // Handle Matching Type (Locations)
    if (question.type === 'matching') {
        console.log("Checking matching question correctness");
        if (!question.correctMatches || typeof selectedAnswers !== 'object' || selectedAnswers === 'Not Answered') {
            console.warn("Invalid or missing user answers for matching question check:", question.id, selectedAnswers);
            return false;
        }

        console.log("Correct matches from question:", JSON.stringify(question.correctMatches));
        const correctLocationIds = Object.keys(question.correctMatches);
        console.log("Correct location IDs:", correctLocationIds);

        // Check if all required locations have been assigned *something* by the user
        const answeredLocations = Object.keys(selectedAnswers).filter(locId => selectedAnswers[locId]?.length > 0);
        console.log("Answered locations:", answeredLocations);
        // Strict check: Uncomment if all locations MUST be answered
        // if (answeredLocations.length !== correctLocationIds.length) {
        //      console.log("Not all locations answered for matching question:", question.id);
        //      return false;
        // }

        let allMatch = true;
        for (const locationId of correctLocationIds) {
            const correct = [...(question.correctMatches[locationId] || [])].sort();
            const user = [...(selectedAnswers[locationId] || [])].sort(); // Handle case where user didn't answer this location

            console.log(`Checking location ${locationId}:`);
            console.log(`  Correct answers: ${JSON.stringify(correct)}`);
            console.log(`  User answers: ${JSON.stringify(user)}`);

            if (user.length !== correct.length || !user.every((value, index) => value === correct[index])) {
                console.log(`  Mismatch found for location ${locationId}`);
                allMatch = false;
                break;
            } else {
                console.log(`  Match for location ${locationId}`);
            }
        }
        console.log("Final matching result:", allMatch);
        return allMatch;
    }
    // Handle Matching Categories Type
    else if (question.type === 'matching_categories') {
        console.log("Checking matching_categories question correctness");
        if (!question.correctMatches || typeof selectedAnswers !== 'object' || selectedAnswers === 'Not Answered') {
            console.warn("Invalid or missing user answers for matching_categories check:", question.id, selectedAnswers);
            return false;
        }

        console.log("Correct matches from question:", JSON.stringify(question.correctMatches));
        console.log("User answers:", JSON.stringify(selectedAnswers));

        // Check if all scenarios have been assigned a category
        const allScenarios = question.scenarios || [];
        console.log("All scenarios:", JSON.stringify(allScenarios.map(s => ({id: s.id, text: s.text}))));

        // Strict check: Uncomment if all scenarios MUST be categorized
        // if (Object.keys(selectedAnswers).length !== allScenarios.length) {
        //     console.log("Not all scenarios categorized:", question.id);
        //     return false;
        // }

        let allMatch = true;
        for (const scenario of allScenarios) {
            const correctCategory = question.correctMatches[scenario.id];
            const userAnswerCategory = selectedAnswers[scenario.id];

            console.log(`Checking scenario ${scenario.id} (${scenario.text}):`);
            console.log(`  Correct category: ${correctCategory}`);
            console.log(`  User category: ${userAnswerCategory || 'not placed'}`);

            // Only check scenarios the user actually placed
            if (userAnswerCategory !== undefined && userAnswerCategory !== correctCategory) {
                console.log(`  Mismatch found for scenario ${scenario.id}`);
                allMatch = false;
                break;
            } else if (userAnswerCategory === correctCategory) {
                console.log(`  Match for scenario ${scenario.id}`);
            } else {
                console.log(`  Scenario ${scenario.id} not placed by user`);
            }
            // If strict checking enabled, also fail if a scenario wasn't placed
            // if (userAnswerCategory === undefined) {
            //     allMatch = false;
            //     break;
            // }
        }
        // Also ensure no scenarios are left in the source list if strict
        const remainingDraggables = optionsContainerElement?.querySelectorAll('#draggableControlsContainer .draggable-control');
        console.log(`Remaining draggables in source list: ${remainingDraggables?.length || 0}`);
        // if (remainingDraggables && remainingDraggables.length > 0) {
        //     allMatch = false;
        // }

        console.log("Final matching_categories result:", allMatch);
        return allMatch;
    }
    // Handle Ordering Type
    else if (question.type === 'ordering') {
        console.log("Checking ordering question correctness");
        if (!question.correctOrder || !Array.isArray(selectedAnswers) || selectedAnswers === 'Not Answered') {
            console.warn("Invalid or missing user answers for ordering question check:", question.id, selectedAnswers);
            return false;
        }

        console.log("Correct order:", JSON.stringify(question.correctOrder));
        console.log("User order:", JSON.stringify(selectedAnswers));

        if (selectedAnswers.length !== question.correctOrder.length) {
            console.log("Length mismatch - user didn't place all items");
            console.log(`User placed ${selectedAnswers.length} items, expected ${question.correctOrder.length}`);
            return false; // User didn't place all items
        }

        const isCorrect = selectedAnswers.every((item, index) => {
            const matches = item === question.correctOrder[index];
            console.log(`Position ${index+1}: User placed "${item}", correct is "${question.correctOrder[index]}" - ${matches ? 'MATCH' : 'MISMATCH'}`);
            return matches;
        });

        console.log("Final ordering result:", isCorrect);
        return isCorrect;
    }

    // Handle Multiple Choice / Multiple Answer Type
    const isMultipleAnswer = Array.isArray(question.answer);
    if (isMultipleAnswer) {
        const correctAnswersSet = new Set(question.answer);
        const selectedAnswersSet = new Set(Array.isArray(selectedAnswers) ? selectedAnswers : [selectedAnswers]);
        return selectedAnswersSet.size === correctAnswersSet.size &&
               [...selectedAnswersSet].every(value => correctAnswersSet.has(value));
    }

    // Handle Single Answer Type
    const singleSelectedAnswer = Array.isArray(selectedAnswers) ? selectedAnswers[0] : selectedAnswers;
    return singleSelectedAnswer === question.answer;
}


// Displays the feedback message (Correct/Incorrect)
function displayFeedbackMessage(message, color) {
    if (resultContainerElement) {
        resultContainerElement.innerHTML = `<div class="feedback" style="color: ${color};">${message}</div>`;
    }
}

// Displays visual feedback on options (highlighting correct/incorrect)
function displayAnswerFeedback(question, selectedOptions, selectedAnswers, isCorrect) {
    if (!resultContainerElement) return;

    // --- Handle Matching Type Feedback (Locations) ---
    if (question.type === 'matching') {
        if (isCorrect) {
            displayFeedbackMessage('Correct! Well done!', 'var(--success-color)');
        } else {
            displayFeedbackMessage('Incorrect!', 'var(--danger-color)');
        }

        const allDropZones = optionsContainerElement ? optionsContainerElement.querySelectorAll('.locations-container .drop-zone') : [];
        allDropZones.forEach(zone => {
            const locationId = zone.dataset.locationId;
            const droppedControlEl = zone.querySelector('.draggable-control');
            const droppedValue = droppedControlEl ? droppedControlEl.dataset.control : null;
            const correctAnswersForLocation = question.correctMatches[locationId] || [];

            zone.classList.remove('over', 'correct', 'incorrect'); // Clear states
            zone.style.borderColor = ''; // Reset border

            if (!droppedValue) { // Empty drop zone
                zone.style.borderColor = 'var(--warning-color)'; // Indicate missed/empty
                // zone.classList.add('incorrect'); // Don't mark empty as incorrect unless required
            } else {
                const isSelectionCorrect = correctAnswersForLocation.includes(droppedValue);
                zone.style.borderColor = isSelectionCorrect ? 'var(--success-color)' : 'var(--danger-color)';
                zone.classList.add(isSelectionCorrect ? 'correct' : 'incorrect');
                if (droppedControlEl) droppedControlEl.style.borderColor = isSelectionCorrect ? 'var(--success-color)' : 'var(--danger-color)';
            }
        });
        return;
    }
    // --- Handle Matching Categories Feedback ---
    else if (question.type === 'matching_categories') {
         if (isCorrect) {
            displayFeedbackMessage('Correct! Well done!', 'var(--success-color)');
        } else {
            displayFeedbackMessage('Incorrect!', 'var(--danger-color)');
        }
        const allCategoryItemDropZones = optionsContainerElement ? optionsContainerElement.querySelectorAll('.categories-container .category-item-drop-zone') : [];
        allCategoryItemDropZones.forEach(zone => {
            const categoryName = zone.dataset.locationId; // Category name stored here
            zone.classList.remove('over', 'correct', 'incorrect'); // Clear states
            zone.style.borderColor = ''; // Reset border color

            const droppedScenario = zone.querySelector('.draggable-control.scenario-item');
            if (droppedScenario) {
                const scenarioId = droppedScenario.dataset.control;
                const correctCategory = question.correctMatches[scenarioId];
                const isPlacementCorrect = categoryName === correctCategory;

                droppedScenario.style.borderColor = isPlacementCorrect ? 'var(--success-color)' : 'var(--danger-color)';
                droppedScenario.classList.add(isPlacementCorrect ? 'correct' : 'incorrect');
                // Highlight the zone itself based on the item inside it
                zone.classList.add(isPlacementCorrect ? 'correct' : 'incorrect');
                zone.style.borderColor = isPlacementCorrect ? 'var(--success-color)' : 'var(--danger-color)';
            } else {
                 zone.style.borderColor = 'var(--warning-color)'; // Mark empty zones
            }
        });
         // Highlight scenarios left in the source list
         const sourceListScenarios = optionsContainerElement?.querySelectorAll('#draggableControlsContainer .scenario-item');
         sourceListScenarios?.forEach(el => {
             el.style.borderColor = 'var(--warning-color)'; // Mark unplaced items as warning/incorrect
             el.classList.add('incorrect');
         });
        return;
    }
    // --- Handle Ordering Feedback ---
    else if (question.type === 'ordering') {
        if (isCorrect) {
            displayFeedbackMessage('Correct! Well done!', 'var(--success-color)');
        } else {
            displayFeedbackMessage('Incorrect!', 'var(--danger-color)');
        }
        const allDropZones = optionsContainerElement ? optionsContainerElement.querySelectorAll('.ordering-drop-zone') : [];
        allDropZones.forEach((zone, index) => {
            const droppedItemEl = zone.querySelector('.draggable-control');
            const droppedValue = droppedItemEl ? droppedItemEl.dataset.control : null;
            const correctValue = question.correctOrder[index];

            zone.classList.remove('over', 'correct', 'incorrect'); // Clear states
            zone.style.borderColor = ''; // Reset border

            if (!droppedValue) {
                zone.style.borderColor = 'var(--warning-color)'; // Indicate empty
            } else {
                const isPlacementCorrect = droppedValue === correctValue;
                zone.style.borderColor = isPlacementCorrect ? 'var(--success-color)' : 'var(--danger-color)';
                zone.classList.add(isPlacementCorrect ? 'correct' : 'incorrect');
                if (droppedItemEl) droppedItemEl.style.borderColor = isPlacementCorrect ? 'var(--success-color)' : 'var(--danger-color)';
            }
        });
        return;
    }


    // --- Handle Non-Matching Type Feedback (Radio/Checkbox) ---
    const allOptionInputs = optionsContainerElement ? optionsContainerElement.querySelectorAll('input') : [];
    const isMultipleAnswer = Array.isArray(question.answer);

    if (isCorrect) {
        displayFeedbackMessage('Correct! Well done!', 'var(--success-color)');
        selectedOptions?.forEach(opt => highlightOption(opt, 'var(--success-color)'));
    } else {
        displayFeedbackMessage('Incorrect!', 'var(--danger-color)');
        allOptionInputs.forEach(opt => {
            const userAnswersArray = Array.isArray(selectedAnswers) ? selectedAnswers : [selectedAnswers];
            const isSelected = userAnswersArray.includes(opt.value);
            const isActuallyCorrect = isMultipleAnswer ? question.answer.includes(opt.value) : opt.value === question.answer;

            if (isSelected && !isActuallyCorrect) {
                highlightOption(opt, 'var(--danger-color)'); // Highlight selected wrong answer
            } else if (isActuallyCorrect) {
                highlightOption(opt, 'var(--success-color)'); // Highlight correct answer
            }
        });
    }
}

// Helper to highlight a specific option (Radio/Checkbox)
function highlightOption(optionInput, color) {
    const span = optionInput.parentElement?.querySelector('span'); // Optional chaining
    if (span) {
        span.style.backgroundColor = color;
        span.style.color = 'white';
    }
}

// Displays the explanation box if applicable (Handles different explanation structures)
function displayExplanation(question) {
    // For non-matching/non-ordering types with a single explanation
    if (question.type !== 'matching' && question.type !== 'matching_categories' && question.type !== 'ordering' && resultContainerElement && quizSettings.showExplanation && question.explanation) {
        const explanationDiv = document.createElement('div');
        explanationDiv.id = 'explanationBox';
        explanationDiv.innerHTML = `<strong>Explanation:</strong><br>${formatExplanation(question.explanation)}`;
        resultContainerElement.appendChild(explanationDiv);
    }
    // Display explanation for ordering type if available
    else if (question.type === 'ordering' && resultContainerElement && quizSettings.showExplanation && question.explanation) {
        const explanationDiv = document.createElement('div');
        explanationDiv.id = 'explanationBox';
        explanationDiv.innerHTML = `<strong>Explanation:</strong><br>${formatExplanation(question.explanation)}`;
        resultContainerElement.appendChild(explanationDiv);
    }
    // Note: Explanations for matching types are handled within handleMatchingAnswer
}


// --- End of Modularized Answer Checking ---

// Format explanation text
function formatExplanation(text) {
  if (!text) return '';

  // Handle asterisks as bold text
  text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Convert line breaks to <br> tags
  text = text.replace(/\n/g, '<br>');

  // Handle numbered lists (lines starting with numbers)
  text = text.replace(/(\d+\.\s+[^\n]+)/g, '<li>$1</li>');

  // Wrap lists in <ol> tags if we found numbered items
  if (text.includes('<li>')) {
    text = '<ol>' + text + '</ol>';
  }

  // Convert URLs to clickable links
  text = text.replace(/(https?:\/\/[^\s<]+)/g, '<a href="$1" target="_blank" class="explanation-link">$1</a>');

  // Ensure no text is truncated by adding proper closing tags
  // This helps prevent issues with malformed HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = text;
  text = tempDiv.innerHTML;

  return text;
}

// Move to next question (handles regular and acronym modes)
// Make async to handle AI regeneration
async function nextQuestion() {
  // --- Handle Acronym Mode ---
  if (currentMode === 'acronyms') {
      console.log("Next Question called in Acronym mode, loading next set...");
      loadNextAcronymSet(); // This function already calls displayAcronyms
      return; // Exit early for acronym mode
  }

  // --- Handle AI Mode (No Regeneration Needed Here Anymore) ---
  // The questions are generated upfront in startQuiz for AI mode now.

  // --- Handle Non-AI Modes (and AI mode now behaves like others) ---
  if (currentMode === 'select') return; // Do nothing in select mode

  currentQuestionIndex++;
  displayQuestion(); // This handles displaying the next question from the pre-generated list
}

// --- Quiz Completion ---

// Finish quiz and show results (for regular modes)
function finishQuiz() {
  clearInterval(timerInterval);
  clearInterval(examTimerInterval);

  const quizTime = Math.round((Date.now() - quizStartTime) / 1000);
  const accuracy = currentQuestions.length > 0 ? Math.round((correctAnswers / currentQuestions.length) * 100) : 0;

  // Revert quizContainer to show results (innerHTML is okay here as it's a final state)
  quizContainer.innerHTML = generateResultsHTML(accuracy, quizTime);

  // Populate the question report after the HTML is set
  populateQuestionReport();

  // Only save overall quiz result if not in 'select' mode
  if (currentMode !== 'select') {
    saveQuizResult(currentMode, currentQuestions.length, correctAnswers, quizTime);
  }

  updateSectionProgressBars(); // Update section progress after finishing a quiz
  updateDifficultyProgressBars();
  updateWeeklyProgressBar(); // Update weekly progress bar after finishing a quiz

  // Update daily progress after finishing a quiz
  updateDailyProgressBars();

  // Add listeners for the new buttons in the results view
  attachResultsViewListeners();

  if (accuracy >= 70) createConfetti();
}

// Special finish function for single question quiz (select/acronym mode)
function finishSingleQuestionQuiz() {
    // The feedback is already shown by handleRegularAnswer or handleMatchingAnswer
    // Or for acronyms, the answer is just revealed.
    // We just need to ensure the "Back to List" or "Next Acronym" button is enabled
    const backToListBtn = document.getElementById('backToListBtn');
    if (backToListBtn) {
        backToListBtn.disabled = false;
    }
    const nextAcronymBtn = document.getElementById('nextAcronymBtn');
    if (nextAcronymBtn) {
        nextAcronymBtn.disabled = false;
    }
    // No confetti, no saving overall results for these modes yet
}

// Generates the HTML for the results screen
function generateResultsHTML(accuracy, quizTime) {
    // Regular results screen
    return `
        <h2>Quiz Complete!</h2>
        <div class="feedback">You scored ${correctAnswers} out of ${currentQuestions.length} (${accuracy}%)</div>

        <div class="stats-grid">
          <div class="stat-card">
            <h3>Questions</h3>
            <div class="stat-value">${currentQuestions.length}</div>
          </div>
          <div class="stat-card">
            <h3>Correct</h3>
            <div class="stat-value">${correctAnswers}</div>
          </div>
          <div class="stat-card">
            <h3>Accuracy</h3>
            <div class="stat-value">${accuracy}%</div>
          </div>
          <div class="stat-card">
            <h3>Time</h3>
            <div class="stat-value">${formatTime(quizTime)}</div>
          </div>
        </div>

        <div class="report">
          <h3>Question Report</h3>
          <div id="questionReport"></div>
        </div>

        <div class="buttons">
          <button id="retryQuizBtn" class="button">Retry Quiz</button>
          <button id="returnHomeBtn" class="button">Return to Home</button>
        </div>
      `;
}

// Populates the detailed question report in the results view
function populateQuestionReport() {
    const questionReport = document.getElementById('questionReport');
    if (!questionReport) return;

    questionReport.innerHTML = ''; // Clear previous report
    currentQuestions.forEach((question, index) => {
        const answerData = currentQuizAnswers[index] || { userAnswer: 'Not answered', isCorrect: false };
        const isMultipleAnswer = Array.isArray(question.answer); // For non-matching
        let userAnswerText = 'Not Answered';
        let correctAnswerDisplay = '';

        // Format user answer and correct answer based on type
        if (question.type === 'matching') {
            if (typeof answerData.userAnswer === 'object' && answerData.userAnswer !== null && answerData.userAnswer !== 'Not Answered') {
                userAnswerText = Object.entries(answerData.userAnswer)
                    .map(([locId, controls]) => {
                        const loc = question.locations.find(l => l.id === locId);
                        const controlsText = Array.isArray(controls) ? controls.join(', ') : 'None';
                        return `${loc ? loc.name : locId}: ${controlsText || 'None'}`;
                    })
                    .join('<br>');
            }
            if (!answerData.isCorrect) {
                 correctAnswerDisplay = '<p style="color: var(--success-color); font-weight: bold;">Correct matches:<br>';
                 Object.entries(question.correctMatches).forEach(([locId, controls]) => {
                     const loc = question.locations.find(l => l.id === locId);
                     correctAnswerDisplay += `${loc ? loc.name : locId}: ${controls.join(', ')}<br>`;
                 });
                 correctAnswerDisplay += '</p>';
            }
        } else if (question.type === 'matching_categories') {
             if (typeof answerData.userAnswer === 'object' && answerData.userAnswer !== null && answerData.userAnswer !== 'Not Answered') {
                 // Group scenarios by the category the user placed them in
                 const answersByCategory = {};
                 Object.entries(answerData.userAnswer).forEach(([scenarioId, categoryName]) => {
                     if (!answersByCategory[categoryName]) {
                         answersByCategory[categoryName] = [];
                     }
                     const scenario = question.scenarios.find(s => s.id === scenarioId);
                     answersByCategory[categoryName].push(scenario ? scenario.text : scenarioId);
                 });
                 userAnswerText = Object.entries(answersByCategory)
                     .map(([categoryName, scenarios]) => `<strong>${categoryName}:</strong><br> - ${scenarios.join('<br> - ')}`)
                     .join('<br><br>');

             }
             if (!answerData.isCorrect) {
                 correctAnswerDisplay = '<p style="color: var(--success-color); font-weight: bold;">Correct matches:<br>';
                 Object.entries(question.correctMatches).forEach(([scenarioId, categoryName]) => {
                     const scenario = question.scenarios.find(s => s.id === scenarioId);
                     correctAnswerDisplay += `${scenario ? scenario.text : scenarioId}: ${categoryName}<br>`;
                 });
                 correctAnswerDisplay += '</p>';
             }
        } else if (question.type === 'ordering') {
            if (Array.isArray(answerData.userAnswer) && answerData.userAnswer.length > 0) {
                userAnswerText = answerData.userAnswer.map((item, i) => `${i + 1}. ${item}`).join('<br>');
            }
            if (!answerData.isCorrect) {
                correctAnswerDisplay = '<p style="color: var(--success-color); font-weight: bold;">Correct order:<br>';
                correctAnswerDisplay += question.correctOrder.map((item, i) => `${i + 1}. ${item}`).join('<br>');
                correctAnswerDisplay += '</p>';
            }
        } else { // Radio/Checkbox
             userAnswerText = Array.isArray(answerData.userAnswer) ? answerData.userAnswer.join(', ') : answerData.userAnswer;
             if (!answerData.isCorrect) {
                 const correctAnswerText = isMultipleAnswer ? question.answer.join(', ') : question.answer;
                 correctAnswerDisplay = `<p style="color: var(--success-color); font-weight: bold;">Correct answer${isMultipleAnswer ? 's' : ''}: ${correctAnswerText}</p>`;
             }
        }


        // --- Determine if explanation exists and prepare HTML ---
        const hasTopLevelExplanation = !!question.explanation; // For non-matching types
        const hasLocationExplanations = question.type === 'matching' && question.locations?.some(loc => !!loc.explanation);
        const hasCategoryExplanations = question.type === 'matching_categories' && !!question.explanations;
        // Ordering questions can also have a top-level explanation
        const showExplanationButton = hasTopLevelExplanation || hasLocationExplanations || hasCategoryExplanations;

        let explanationHTML = 'No explanation available.';
        if (hasTopLevelExplanation) {
            explanationHTML = formatExplanation(question.explanation);
        } else if (hasLocationExplanations) {
            explanationHTML = question.locations
                .filter(loc => !!loc.explanation)
                .map(loc => `<strong>${loc.name}:</strong><br>${formatExplanation(loc.explanation)}`)
                .join('<hr style="margin: 8px 0; border: none; border-top: 1px dashed var(--border-color);">');
        } else if (hasCategoryExplanations) {
             explanationHTML = Object.entries(question.explanations)
                 .map(([catName, text]) => `<strong>${catName}:</strong><br>${formatExplanation(text)}`)
                 .join('<hr style="margin: 8px 0; border: none; border-top: 1px dashed var(--border-color);">');
        }
        // --- End Explanation Logic ---


        // --- Create DOM Elements Programmatically ---
        const resultDiv = document.createElement('div');
        resultDiv.className = 'question-result';
        resultDiv.style.margin = '10px 0';
        resultDiv.style.padding = '15px';
        resultDiv.style.borderRadius = '8px';
        resultDiv.style.backgroundColor = answerData.isCorrect ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)';
        resultDiv.style.borderLeft = `4px solid ${answerData.isCorrect ? 'var(--success-color)' : 'var(--danger-color)'}`;

        const questionP = document.createElement('p');
        // Use questionText for matching/ordering types in report
        const questionTextKey = (question.type === 'matching' || question.type === 'matching_categories' || question.type === 'ordering') ? 'questionText' : 'question';
        questionP.innerHTML = `<strong>Question ${index + 1}:</strong> ${formatQuestionText(question[questionTextKey])}`;
        resultDiv.appendChild(questionP);

        const userAnswerP = document.createElement('p');
        userAnswerP.style.color = answerData.isCorrect ? 'var(--success-color)' : 'var(--danger-color)';
        userAnswerP.style.fontWeight = 'bold';
        userAnswerP.innerHTML = `Your answer: ${userAnswerText}`;
        resultDiv.appendChild(userAnswerP);

        // Append correct answer display if needed
        if (correctAnswerDisplay) {
            const correctAnswerDiv = document.createElement('div');
            correctAnswerDiv.innerHTML = correctAnswerDisplay; // Already contains <p> tag
            resultDiv.appendChild(correctAnswerDiv);
        }

        // Append explanation button if needed
        if (showExplanationButton) {
            const button = document.createElement('button');
            button.className = 'show-explanation-btn';
            button.dataset.questionIndex = index;
            button.textContent = 'Show Explanation';
            resultDiv.appendChild(button);
        }

        // Append explanation content div
        const explanationDiv = document.createElement('div');
        explanationDiv.className = 'explanation-content hidden';
        explanationDiv.id = `explanation-${index}`;
        explanationDiv.style.marginTop = '10px';
        explanationDiv.style.padding = '10px';
        explanationDiv.style.backgroundColor = 'rgba(0,0,0,0.05)';
        explanationDiv.style.borderRadius = '4px';
        explanationDiv.innerHTML = `<strong>Explanation:</strong><br>${explanationHTML}`;
        resultDiv.appendChild(explanationDiv);

        // Append the constructed result item to the report container
        questionReport.appendChild(resultDiv);
        // --- End DOM Element Creation ---
    });
}


// Attaches event listeners to buttons in the results view
function attachResultsViewListeners() {
    const retryBtn = document.getElementById('retryQuizBtn');
    const returnHomeBtn = document.getElementById('returnHomeBtn');
    const questionReportContainer = document.getElementById('questionReport');

    if (retryBtn) retryBtn.addEventListener('click', () => startQuiz(currentMode)); // Retry works for regular modes
    if (returnHomeBtn) {
        returnHomeBtn.addEventListener('click', () => {
            showView('home');
            if(homeButton) homeButton.classList.add('active');
            if(statsButton) statsButton.classList.remove('active');
        });
    }

    // Event delegation for explanation buttons
    if (questionReportContainer) {
        questionReportContainer.addEventListener('click', (event) => {
            if (event.target.classList.contains('show-explanation-btn')) {
                const button = event.target;
                const questionIndex = button.dataset.questionIndex;
                const explanationDiv = document.getElementById(`explanation-${questionIndex}`);
                if (explanationDiv) {
                    const isHidden = explanationDiv.classList.toggle('hidden');
                    button.textContent = isHidden ? 'Show Explanation' : 'Hide Explanation';
                }
            }
        });
    }
}


// Format time as mm:ss
function formatTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
}

// --- User Data & Statistics ---

// Global variable to hold the *current user's* data
let userData = {};

// Helper function to get the current nickname
function getCurrentNickname() {
    return currentNickname || localStorage.getItem('quizUserNickname');
}

// Helper function to initialize default user data structure (returns a new object)
function createDefaultUserData() {
    console.log("Creating default user data structure.");
    return {
        stats: {
          overall: { totalQuestions: 0, correctAnswers: 0, totalTime: 0, quizzesTaken: 0 },
          A: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
          B: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 },
          C: { totalQuestions: 0, correctAnswers: 0, totalTime: 0 } // Initialize C
        },
        questionHistory: {},
        favoriteQuestions: [],
        quizHistory: [],
        difficultyOverrides: {},
        gamification: {
            xp: 0,
            level: 1,
            achievements: {},
            streak: {
                current: 0,
                lastQuizDate: null
            }
        }
    };
}


// Save user data to localStorage for the specific user
function saveUserData(nickname) {
    if (!nickname) {
        console.error("Cannot save user data without a nickname.");
        return;
    }
    try {
        let allUsersData = {};
        const storedData = localStorage.getItem(ALL_USERS_STORAGE_KEY);
        if (storedData) {
            allUsersData = JSON.parse(storedData);
        }
        // Update the data for the current user
        allUsersData[nickname] = userData;
        localStorage.setItem(ALL_USERS_STORAGE_KEY, JSON.stringify(allUsersData));
    } catch (e) {
        console.error(`Error saving user data for ${nickname} to localStorage:`, e);
    }
}

// Load user data from localStorage for the specific user
function loadUserData(nickname) {
    if (!nickname) {
        console.error("Cannot load user data without a nickname.");
        userData = createDefaultUserData(); // Reset global userData if no nickname
        return;
    }
    console.log(`Loading data for user: ${nickname}`);
    let allUsersData = {};
    let savedData = null;
    try {
        savedData = localStorage.getItem(ALL_USERS_STORAGE_KEY);
    } catch (e) {
        console.error('Error reading all users data from localStorage:', e);
        showFeedback('Could not load saved progress due to storage error.');
        userData = createDefaultUserData(); // Use default for current session
        return; // Exit function
    }

    if (savedData) {
        try {
            allUsersData = JSON.parse(savedData);
        } catch (e) {
            console.error('Error parsing all users data from localStorage:', e);
            showFeedback('Failed to parse saved progress. Resetting data for this session.');
            allUsersData = {}; // Reset if parsing fails
        }
    }

    // Check if data exists for this specific user
    if (allUsersData[nickname]) {
        console.log(`Data found for ${nickname}. Merging...`);
        // Load the specific user's data
        const loadedData = allUsersData[nickname];
        // Perform a simple merge/assignment (ensure structure is valid)
        userData = {
            stats: { ...(createDefaultUserData().stats), ...(loadedData.stats || {}) }, // Merge stats ensuring all categories exist
            questionHistory: loadedData.questionHistory || {},
            favoriteQuestions: loadedData.favoriteQuestions || [],
            quizHistory: loadedData.quizHistory || [],
            difficultyOverrides: loadedData.difficultyOverrides || {},
            gamification: { ...(createDefaultUserData().gamification), ...(loadedData.gamification || {}) } // Merge gamification data
        };
         // Ensure all expected stat categories exist after merge
         ['overall', 'A', 'B', 'C'].forEach(cat => {
            if (!userData.stats[cat]) {
                userData.stats[cat] = { totalQuestions: 0, correctAnswers: 0, totalTime: 0, quizzesTaken: cat === 'overall' ? 0 : undefined };
            }
        });
    } else {
        console.log(`No data found for ${nickname}. Initializing default data.`);
        // Initialize default structure if no saved data exists for this user
        userData = createDefaultUserData();
        // Optionally save this new user structure immediately
        saveUserData(nickname);
    }

  // Note: UI updates (updateStatsDisplay, updateFavoritesDisplay) are now called
  // after loadUserData in saveNickname and loadAndDisplayNickname
  updateSectionProgressBars(); // Update section progress after loading data
  updateDifficultyProgressBars();
  updateWeeklyProgressBar(); // Update weekly progress bar after loading data

  // Update daily progress after loading data
  updateDailyProgressBars();
}

// --- Daily Progress Bar Logic ---

function updateDailyProgressBars() {
  const dailyProgressA = document.getElementById('dailyProgressA');
  const dailyRemainingA = document.getElementById('dailyRemainingA');
  const dailyProgressB = document.getElementById('dailyProgressB');
  const dailyRemainingB = document.getElementById('dailyRemainingB');

  if (!dailyProgressA || !dailyRemainingA || !dailyProgressB || !dailyRemainingB) {
    // Elements might not exist if home view isn't rendered yet
    // console.log("Daily progress elements not found, skipping update.");
    return;
  }

  if (!allQuestions || allQuestions.length === 0 || !userData || !userData.questionHistory) {
    console.log("Required data for daily progress not ready.");
    // Reset display if data is missing
    dailyProgressA.style.width = '0%';
    dailyRemainingA.textContent = '0/0 remaining';
    dailyProgressB.style.width = '0%';
    dailyRemainingB.textContent = '0/0 remaining';
    return;
  }

  const twentyFourHoursAgo = Date.now() - (24 * 60 * 60 * 1000);

  // Calculate for Section A
  const totalQuestionsA = allQuestions.filter(q => q.category === 'A').length;
  let correctlyAnsweredTodayA = 0;
  allQuestions.forEach(q => {
    if (q.category === 'A') {
      const historyEntry = userData.questionHistory[q.id];
      if (historyEntry && historyEntry.correct === true && historyEntry.timestamp && historyEntry.timestamp >= twentyFourHoursAgo) {
        correctlyAnsweredTodayA++;
      }
    }
  });
  const remainingA = totalQuestionsA - correctlyAnsweredTodayA;
  const percentageA = totalQuestionsA > 0 ? (correctlyAnsweredTodayA / totalQuestionsA) * 100 : 0;

  dailyProgressA.style.width = `${percentageA}%`;
  dailyRemainingA.textContent = `${remainingA}/${totalQuestionsA} remaining`;

  // Calculate for Section B
  const totalQuestionsB = allQuestions.filter(q => q.category === 'B').length;
  let correctlyAnsweredTodayB = 0;
  allQuestions.forEach(q => {
    if (q.category === 'B') {
      const historyEntry = userData.questionHistory[q.id];
      if (historyEntry && historyEntry.correct === true && historyEntry.timestamp && historyEntry.timestamp >= twentyFourHoursAgo) {
        correctlyAnsweredTodayB++;
      }
    }
  });
  const remainingB = totalQuestionsB - correctlyAnsweredTodayB;
  const percentageB = totalQuestionsB > 0 ? (correctlyAnsweredTodayB / totalQuestionsB) * 100 : 0;

  dailyProgressB.style.width = `${percentageB}%`;
  dailyRemainingB.textContent = `${remainingB}/${totalQuestionsB} remaining`;

  console.log(`Daily Progress Updated: A (${correctlyAnsweredTodayA}/${totalQuestionsA}), B (${correctlyAnsweredTodayB}/${totalQuestionsB})`);
}



// --- Weekly Progress Bar Logic ---
function updateWeeklyProgressBar() {
  if (!allQuestions || allQuestions.length === 0 || !userData || !userData.questionHistory) {
    console.log("Required data for weekly progress not ready.");
    if (weeklyProgressBar) {
      weeklyProgressBar.style.width = '0%';
      weeklyProgressBar.style.backgroundColor = 'var(--primary-color)';
    }
    return;
  }

  // Check if we need to reset the weekly progress
  const now = new Date();
  const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const resetDay = 0; // Sunday is reset day

  // Get the last reset timestamp from userData or initialize it
  if (!userData.weeklyProgressReset) {
    userData.weeklyProgressReset = {
      lastResetTimestamp: 0,
      resetDay: resetDay
    };
  }

  // Check if we need to reset based on the day
  const lastResetDate = new Date(userData.weeklyProgressReset.lastResetTimestamp);
  const lastResetDay = lastResetDate.getDay();

  // Reset if it's reset day and we haven't reset yet today, or if we've passed the reset day since last reset
  if ((currentDay === resetDay && lastResetDate.toDateString() !== now.toDateString()) ||
      (lastResetDay > currentDay) ||
      (now - lastResetDate > 7 * 24 * 60 * 60 * 1000)) {

    // Reset the progress
    console.log("Resetting weekly progress...");
    userData.weeklyProgressReset.lastResetTimestamp = now.getTime();

    // Save the updated userData
    const nickname = getCurrentNickname();
    if (nickname) {
      saveUserData(nickname);
    }

    // Add visual feedback for reset
    if (weeklyProgressBar) {
      weeklyProgressBar.classList.add('progress-bar-reset');
      setTimeout(() => {
        weeklyProgressBar.classList.remove('progress-bar-reset');
      }, 500);

      // Show reset feedback to user
      showFeedback("Weekly progress has been reset!", "var(--info-color)");
    }
  }

  // Calculate progress since last reset
  const lastResetTimestamp = userData.weeklyProgressReset.lastResetTimestamp;
  let totalQuestions = allQuestions.length;
  let correctlyAnsweredThisWeek = 0;

  // Count questions answered correctly since last reset
  allQuestions.forEach(q => {
    const historyEntry = userData.questionHistory[q.id];
    if (historyEntry && historyEntry.correct === true && historyEntry.timestamp && historyEntry.timestamp >= lastResetTimestamp) {
      correctlyAnsweredThisWeek++;
    }
  });

  // Calculate percentage and update progress bar
  const percentage = totalQuestions > 0 ? (correctlyAnsweredThisWeek / totalQuestions) * 100 : 0;
  if (weeklyProgressBar) {
    // Apply smooth transition
    weeklyProgressBar.style.transition = 'width 0.8s ease-in-out, background-color 0.8s ease-in-out';
    weeklyProgressBar.style.width = `${percentage}%`;

    // Change color based on progress
    if (percentage < 30) {
      weeklyProgressBar.style.backgroundColor = 'var(--warning-color)';
    } else if (percentage < 70) {
      weeklyProgressBar.style.backgroundColor = 'var(--primary-color)';
    } else {
      weeklyProgressBar.style.backgroundColor = 'var(--success-color)';
    }

    // Add tooltip to show progress information
    weeklyProgressBar.title = `Weekly Progress: ${correctlyAnsweredThisWeek}/${totalQuestions} (${percentage.toFixed(1)}%)`;

    // Add ARIA attributes for accessibility
    weeklyProgressBar.setAttribute('aria-valuenow', percentage.toFixed(1));
    weeklyProgressBar.setAttribute('aria-valuemin', '0');
    weeklyProgressBar.setAttribute('aria-valuemax', '100');
    weeklyProgressBar.setAttribute('role', 'progressbar');
    weeklyProgressBar.setAttribute('aria-label', 'Weekly progress');

    console.log(`Weekly Progress Updated: ${correctlyAnsweredThisWeek}/${totalQuestions} (${percentage.toFixed(1)}%)`);

    // Update next reset date information
    updateNextResetInfo(resetDay);
  }
}

// Display information about the next progress bar reset
function updateNextResetInfo(resetDay) {
  try {
    // Find or create the reset info element
    let resetInfoElement = document.getElementById('weeklyResetInfo');
    if (!resetInfoElement) {
      resetInfoElement = document.createElement('div');
      resetInfoElement.id = 'weeklyResetInfo';
      resetInfoElement.className = 'weekly-reset-info';

      // Insert after the weekly progress bar
      if (weeklyProgressBar && weeklyProgressBar.parentNode) {
        weeklyProgressBar.parentNode.insertAdjacentElement('afterend', resetInfoElement);
      }
    }

    // Calculate days until next reset
    const now = new Date();
    const currentDay = now.getDay();
    const daysUntilReset = (resetDay - currentDay + 7) % 7;

    // Format the next reset date
    const nextResetDate = new Date(now);
    nextResetDate.setDate(now.getDate() + daysUntilReset);
    const formattedDate = nextResetDate.toLocaleDateString(undefined, {
      weekday: 'long',
      month: 'short',
      day: 'numeric'
    });

    // Update the reset info text
    if (daysUntilReset === 0) {
      resetInfoElement.textContent = `Progress resets today`;
    } else if (daysUntilReset === 1) {
      resetInfoElement.textContent = `Progress resets tomorrow`;
    } else {
      resetInfoElement.textContent = `Progress resets on ${formattedDate}`;
    }

    // Add tooltip with more information
    resetInfoElement.title = `Weekly progress resets every Sunday. Next reset: ${formattedDate}`;

  } catch (error) {
    console.error("Error updating reset info:", error);
  }
}

// --- Section Progress Bar Logic (Home Screen Filters) ---

function updateSectionProgressBars() {
  const sections = ['A', 'B', 'C']; // Sections to update

  if (!allQuestions || allQuestions.length === 0 || !userData || !userData.questionHistory) {
    console.log("Required data for section progress not ready.");
    // Optionally reset bars if data is missing
    sections.forEach(section => {
      const progressBar = document.querySelector(`.categories-filter .filter-option[data-category='${section}'] .filter-option-progress`);
      if (progressBar) progressBar.style.width = '0%';
    });
    return;
  }

  sections.forEach(section => {
    const sectionQuestions = allQuestions.filter(q => q.category === section);
    const totalSectionQuestions = sectionQuestions.length;
    let correctSectionAnswers = 0;

    if (totalSectionQuestions > 0) {
      sectionQuestions.forEach(q => {
        const historyEntry = userData.questionHistory[q.id];
        // Count as correct if it exists in history and was marked correct
        if (historyEntry && historyEntry.correct === true) {
          correctSectionAnswers++;
        }
      });
    }

    const percentage = totalSectionQuestions > 0 ? (correctSectionAnswers / totalSectionQuestions) * 100 : 0;
    const progressBar = document.querySelector(`.categories-filter .filter-option[data-category='${section}'] .filter-option-progress`);

    if (progressBar) {
      progressBar.style.width = `${percentage}%`;
      console.log(`Section ${section} Progress Updated: ${correctSectionAnswers}/${totalSectionQuestions} (${percentage.toFixed(1)}%)`);
    } else {
      // console.warn(`Progress bar for section ${section} not found.`);
    }
  });
}

// --- Difficulty Progress Bar Logic (Home Screen Filters) ---
function updateDifficultyProgressBars() {
  const difficulties = ['performance', 'hard', 'multiple choice'];
  if (!allQuestions || allQuestions.length === 0 || !userData || !userData.questionHistory) {
    difficulties.forEach(difficulty => {
      const progressBar = document.querySelector(`.difficulty-filter .filter-option[data-difficulty='${difficulty}'] .filter-option-progress`);
      if (progressBar) progressBar.style.width = '0%';
    });
    return;
  }
  difficulties.forEach(difficulty => {
    // For 'performance', include all questions with difficulty 'performance', 'matching', 'matching_categories', or 'ordering'
    let difficultyQuestions;
    if (difficulty === 'performance') {
      difficultyQuestions = allQuestions.filter(q => ['performance', 'matching', 'matching_categories', 'ordering'].includes(q.difficulty));
    } else {
      difficultyQuestions = allQuestions.filter(q => (q.difficulty === difficulty));
    }
    const total = difficultyQuestions.length;
    let correct = 0;
    if (total > 0) {
      difficultyQuestions.forEach(q => {
        const historyEntry = userData.questionHistory[q.id];
        if (historyEntry && historyEntry.correct === true) correct++;
      });
    }
    const percentage = total > 0 ? (correct / total) * 100 : 0;
    const progressBar = document.querySelector(`.difficulty-filter .filter-option[data-difficulty='${difficulty}'] .filter-option-progress`);
    if (progressBar) progressBar.style.width = `${percentage}%`;
  });
}
// --- End Difficulty Progress Bar Logic ---

// --- End Section Progress Bar Logic ---
// --- End Daily Progress Bar Logic ---


// Get effective difficulty considering overrides (uses global userData)
function getEffectiveDifficulty(questionId) {
  // Ensure difficultyOverrides exists
  const overrides = userData.difficultyOverrides || {};
  if (overrides[questionId] === 'hard') {
    return 'hard'; // Return 'hard' if overridden
  }
  // Ensure allQuestions is populated before accessing it
  const question = allQuestions?.find(q => q.id === questionId);
  return question?.difficulty || 'medium'; // Default to medium
}

// Save individual question result (uses global userData)
function saveQuestionResult(questionId, correct, timeOrReason) {
    try {
        const nickname = getCurrentNickname();
        if (!nickname) {
            console.warn("Cannot save question result: No user nickname found");
            showFeedback("Login required to save progress", "var(--warning-color)");
            return; // Don't save if no user context
        }

        if (!userData) {
            console.error("Cannot save question result: userData is null or undefined");
            userData = createDefaultUserData(); // Create default data if missing
            showFeedback("Creating new user data", "var(--warning-color)");
        }

        const question = allQuestions.find(q => q.id === questionId);
        if (!question) {
            console.warn(`Question with ID ${questionId} not found in allQuestions`);
        }

        const category = question?.category;

        // Ensure stats objects exist within the current userData
        if (!userData.stats) {
            console.warn("Stats object missing in userData, initializing");
            userData.stats = createDefaultUserData().stats; // Initialize if missing
        }

        if (!userData.stats.overall) {
            console.warn("Overall stats missing in userData, initializing");
            userData.stats.overall = createDefaultUserData().stats.overall;
        }

        if (category && !userData.stats[category]) {
            userData.stats[category] = createDefaultUserData().stats[category] || { totalQuestions: 0, correctAnswers: 0, totalTime: 0 };
            console.warn(`Initialized missing stats category: ${category} in userData`);
        }

        // Update overall stats
        userData.stats.overall.totalQuestions = (userData.stats.overall.totalQuestions || 0) + 1;
        if (correct) userData.stats.overall.correctAnswers = (userData.stats.overall.correctAnswers || 0) + 1;
        if (typeof timeOrReason === 'number') userData.stats.overall.totalTime = (userData.stats.overall.totalTime || 0) + timeOrReason;

        // Update category-specific stats
        if (category && userData.stats[category]) {
            userData.stats[category].totalQuestions = (userData.stats[category].totalQuestions || 0) + 1;
            if (correct) userData.stats[category].correctAnswers = (userData.stats[category].correctAnswers || 0) + 1;
            if (typeof timeOrReason === 'number') userData.stats[category].totalTime = (userData.stats[category].totalTime || 0) + timeOrReason;
        } else if (category) {
            console.warn(`Category ${category} stats structure missing during save.`);
        }

        // Ensure questionHistory exists
        if (!userData.questionHistory) {
            console.warn("Question history missing in userData, initializing");
            userData.questionHistory = {};
        }

        // Save question history with timestamp
        userData.questionHistory[questionId] = {
            correct: correct,
            timeOrReason: timeOrReason,
            date: new Date().toISOString(),
            answer: getSelectedAnswer(), // Get the potentially complex answer structure
            timestamp: Date.now() // Add timestamp here
        };

        // Ensure difficultyOverrides exists
        if (!userData.difficultyOverrides) {
            console.warn("Difficulty overrides missing in userData, initializing");
            userData.difficultyOverrides = {};
        }

        // Mark as 'hard' if answered incorrectly
        if (!correct) {
            userData.difficultyOverrides[questionId] = 'hard';
        } else {
            // Optionally remove override if answered correctly?
            // delete userData.difficultyOverrides[questionId];
        }

        // --- Gamification Integration ---
        // Ensure gamification data exists
        if (!userData.gamification) {
            userData.gamification = window.gamification ? {} : null;
        }

        if (userData.gamification && window.gamification) {
            // Award XP for answering the question
            if (correct) {
                // Award XP for correct answer
                window.gamification.awardXP(window.gamification.XP_REWARDS.CORRECT_ANSWER, userData);

                // Update streak
                window.gamification.updateStreak(userData);
            } else {
                // Reset streak on incorrect answer
                if (userData.gamification.streak) {
                    userData.gamification.streak.current = 0;
                }
            }

            // Check for achievements
            window.gamification.checkAchievements(userData);

            // Update XP display
            updateXpDisplay();
        }

        // Save user data
        saveUserData(nickname); // Save after each question result for the current user

        // Update weekly progress bar with visual feedback
        const oldProgressBar = weeklyProgressBar ? weeklyProgressBar.style.width : '0%';
        updateWeeklyProgressBar();

        // Add visual feedback if progress changed significantly
        if (weeklyProgressBar) {
            const newProgressWidth = weeklyProgressBar.style.width;
            if (parseFloat(newProgressWidth) > parseFloat(oldProgressBar) + 5) {
                // Progress increased by more than 5%
                weeklyProgressBar.classList.add('progress-bar-reset');
                setTimeout(() => {
                    weeklyProgressBar.classList.remove('progress-bar-reset');
                }, 500);
            }
        }

    } catch (error) {
        console.error("Error saving question result:", error);
        showFeedback("Error saving progress", "var(--danger-color)");
    }
}

// Save overall quiz result (uses global userData)
function saveQuizResult(mode, questionsCount, correct, time) { // Renamed 'questions' param to 'questionsCount' for clarity
    try {
        const nickname = getCurrentNickname();
        if (!nickname) {
            console.warn("Cannot save quiz result: No user nickname found");
            showFeedback("Login required to save progress", "var(--warning-color)");
            return;
        }

        // Don't save results for 'select' mode in history yet
        if (currentMode === 'select') {
            console.log(`Skipping saving quiz history for mode: ${currentMode}`);
            return;
        }

        if (!userData) {
            console.error("Cannot save quiz result: userData is null or undefined");
            userData = createDefaultUserData(); // Create default data if missing
            showFeedback("Creating new user data", "var(--warning-color)");
        }

        // Ensure stats objects exist
        if (!userData.stats) {
            console.warn("Stats object missing in userData, initializing");
            userData.stats = createDefaultUserData().stats;
        }

        if (!userData.stats.overall) {
            console.warn("Overall stats missing in userData, initializing");
            userData.stats.overall = createDefaultUserData().stats.overall;
        }

        userData.stats.overall.quizzesTaken = (userData.stats.overall.quizzesTaken || 0) + 1;

        // Ensure quiz history exists
        if (!userData.quizHistory) {
            console.warn("Quiz history missing in userData, initializing");
            userData.quizHistory = [];
        }

        // Create the detailed history entry
        const historyEntry = {
            mode: mode,
            questionsCount: questionsCount, // Use the parameter name
            correct: correct,
            accuracy: questionsCount > 0 ? Math.round((correct / questionsCount) * 100) : 0,
            time: time,
            date: new Date().toISOString(),
            // Store copies of the questions and answers for this specific quiz instance
            questions: JSON.parse(JSON.stringify(currentQuestions || [])), // Deep copy with fallback
            answers: JSON.parse(JSON.stringify(currentQuizAnswers || {}))   // Deep copy with fallback
        };

        // Add the new entry to the beginning of the array
        userData.quizHistory.unshift(historyEntry);

        // Keep only the last 10 entries
        if (userData.quizHistory.length > 10) {
            userData.quizHistory = userData.quizHistory.slice(0, 10);
        }

        // --- Gamification Integration ---
        if (!userData.gamification && window.gamification) {
            console.warn("Gamification data missing in userData, initializing");
            userData.gamification = {};
        }

        if (userData.gamification && window.gamification) {
            // Award XP for completing the quiz
            window.gamification.awardXP(window.gamification.XP_REWARDS.QUIZ_COMPLETION, userData);

            // Award bonus XP for perfect score
            if (correct === questionsCount && questionsCount >= 5) {
                window.gamification.awardXP(window.gamification.XP_REWARDS.PERFECT_SCORE, userData);
            }

            // Check for achievements
            window.gamification.checkAchievements(userData);

            // Update XP display
            updateXpDisplay();
        }

        console.log("Saved quiz history entry:", historyEntry);
        console.log("Current quiz history length:", userData.quizHistory.length);

        // Save user data
        saveUserData(nickname); // Save after quiz finishes for the current user

        // Update weekly progress bar with visual feedback
        const oldProgressBar = weeklyProgressBar ? weeklyProgressBar.style.width : '0%';
        updateWeeklyProgressBar();

        // Add visual feedback if progress changed significantly
        if (weeklyProgressBar) {
            const newProgressWidth = weeklyProgressBar.style.width;
            if (parseFloat(newProgressWidth) > parseFloat(oldProgressBar) + 5) {
                // Progress increased by more than 5%
                weeklyProgressBar.classList.add('progress-bar-reset');
                setTimeout(() => {
                    weeklyProgressBar.classList.remove('progress-bar-reset');
                }, 500);
            }
        }

        // Show feedback to user
        showFeedback("Quiz progress saved!", "var(--success-color)");

    } catch (error) {
        console.error("Error saving quiz result:", error);
        showFeedback("Error saving quiz progress", "var(--danger-color)");
    }
}

// Toggle favorite status of the current question (uses global userData)
function toggleFavorite() {
    const nickname = getCurrentNickname();
    if (!nickname) return;

    const question = currentQuestions[currentQuestionIndex];
    if (!question) return;

    if (!userData.favoriteQuestions) userData.favoriteQuestions = []; // Ensure array exists

    const index = userData.favoriteQuestions.indexOf(question.id);
    if (index > -1) {
        userData.favoriteQuestions.splice(index, 1); // Remove if exists
        if (favoriteBtnElement) favoriteBtnElement.classList.remove('active');
        showFeedback('Question removed from favorites');
    } else {
        userData.favoriteQuestions.push(question.id); // Add if not exists
        if (favoriteBtnElement) favoriteBtnElement.classList.add('active');
        showFeedback('Question added to favorites');
    }

    updateFavoritesDisplay(); // Update UI immediately
    saveUserData(nickname); // Save changes for the current user
}

// Add data clearing functionality button (clears current user's data)
function addClearDataButton() {
    const statsContainerEl = document.getElementById('statsContainer');
    if (!statsContainerEl || statsContainerEl.querySelector('#resetProgressBtn')) return;

    const clearButton = document.createElement('button');
    clearButton.id = 'resetProgressBtn';
    clearButton.className = 'button button-danger';
    clearButton.textContent = 'Reset My Progress'; // Changed text
    clearButton.style.marginTop = '20px';
    clearButton.onclick = () => {
        const nickname = getCurrentNickname();
        if (!nickname) {
            showFeedback("Cannot reset progress: No user logged in.");
            return;
        }
        if (confirm(`Are you sure you want to reset all progress for ${nickname}? This cannot be undone.`)) {
            try {
                let allUsersData = {};
                const storedData = localStorage.getItem(ALL_USERS_STORAGE_KEY);
                if (storedData) {
                    allUsersData = JSON.parse(storedData);
                }
                // Replace current user's data with default
                allUsersData[nickname] = createDefaultUserData();
                localStorage.setItem(ALL_USERS_STORAGE_KEY, JSON.stringify(allUsersData));

                // Update global variable and UI
                userData = allUsersData[nickname];
                updateStatsDisplay();
                updateFavoritesDisplay();
                showFeedback(`Progress for ${nickname} has been reset.`);
            } catch (e) {
                console.error(`Error resetting progress for ${nickname}:`, e);
                showFeedback("An error occurred while resetting progress.");
            }
        }
    };
    statsContainerEl.appendChild(clearButton);
}

// Get selected answer(s) from the options (handles different question types)
function getSelectedAnswer() {
    const question = currentQuestions[currentQuestionIndex];
    if (!question) return 'Error: No current question';

    console.log("Getting selected answer for question type:", question.type);
    console.log("Question ID:", question.id);

    // --- Handle Matching Type (Locations) ---
    if (question.type === 'matching') {
        console.log("Getting answers for matching question");
        const userAnswers = {};
        const allDropZones = optionsContainerElement ? optionsContainerElement.querySelectorAll('.locations-container .drop-zone') : [];
        console.log(`Found ${allDropZones.length} drop zones`);

        allDropZones.forEach(zone => {
            const locationId = zone.dataset.locationId;
            if (!userAnswers[locationId]) {
                userAnswers[locationId] = [];
            }
            const droppedControl = zone.querySelector('.draggable-control');
            if (droppedControl) {
                const controlValue = droppedControl.dataset.control || droppedControl.textContent;
                console.log(`Location ${locationId} has control: ${controlValue}`);
                userAnswers[locationId].push(controlValue);
            } else {
                console.log(`Location ${locationId} has no dropped control`);
            }
        });
        const hasSelections = Object.values(userAnswers).some(arr => arr.length > 0);
        console.log("Final user answers for matching:", JSON.stringify(userAnswers));
        console.log("Has selections:", hasSelections);
        return hasSelections ? userAnswers : 'Not Answered';
    }
    // --- Handle Matching Categories Type ---
    else if (question.type === 'matching_categories') {
        console.log("Getting answers for matching_categories question");
        const userAnswers = {}; // scenarioId: categoryName
        const allCategoryItemDropZones = optionsContainerElement ? optionsContainerElement.querySelectorAll('.categories-container .category-item-drop-zone') : [];
        console.log(`Found ${allCategoryItemDropZones.length} category drop zones`);

        allCategoryItemDropZones.forEach(zone => {
            const categoryName = zone.dataset.locationId; // Category name stored here
            const droppedScenario = zone.querySelector('.draggable-control.scenario-item');
            if (droppedScenario) {
                const scenarioId = droppedScenario.dataset.control; // Scenario ID stored in control dataset
                console.log(`Category ${categoryName} has scenario: ${scenarioId}`);
                userAnswers[scenarioId] = categoryName; // Assign category to this scenario
            }
        });
        // Check if any scenarios were dropped
        const hasSelections = Object.keys(userAnswers).length > 0;
        console.log("Final user answers for matching_categories:", JSON.stringify(userAnswers));
        console.log("Has selections:", hasSelections);
        return hasSelections ? userAnswers : 'Not Answered';
    }
    // --- Handle Ordering Type ---
    else if (question.type === 'ordering') {
        console.log("Getting answers for ordering question");
        const orderedItems = [];
        const allDropZones = optionsContainerElement ? optionsContainerElement.querySelectorAll('.ordering-drop-zone') : [];
        console.log(`Found ${allDropZones.length} ordering drop zones`);

        allDropZones.forEach((zone, index) => {
            // Look for any draggable control, not just those with the ordering-item class
            const droppedItem = zone.querySelector('.draggable-control');
            if (droppedItem) {
                // Try to get the value from dataset.control first, then fallback to textContent
                const itemValue = droppedItem.dataset.control || droppedItem.textContent.trim();
                console.log(`Position ${index+1} has item: ${itemValue}`);
                orderedItems.push(itemValue);
            } else {
                console.log(`Position ${index+1} is empty`);
                orderedItems.push(null); // Represent empty slot if needed, or filter later
            }
        });

        // Filter out nulls if you only want placed items, or keep them to check if all slots are filled
        const placedItems = orderedItems.filter(item => item !== null);
        console.log("Final ordered items:", JSON.stringify(placedItems));
        console.log("Items placed:", placedItems.length, "of", orderedItems.length);

        // Only return the answer if all slots are filled
        if (placedItems.length === allDropZones.length) {
            return placedItems;
        } else if (placedItems.length > 0) {
            // Return partial answer
            return placedItems;
        } else {
            return 'Not Answered';
        }
    }

    // --- Handle Radio/Checkbox Types ---
    const selectedOptions = optionsContainerElement ? optionsContainerElement.querySelectorAll('input[name="option"]:checked') : [];
    if (selectedOptions.length === 0) return 'Not Answered';

    const isCheckbox = selectedOptions[0].type === 'checkbox';
    if (isCheckbox) {
        return Array.from(selectedOptions).map(opt => opt.value);
    } else {
        return selectedOptions[0].value;
    }
}

// Update stats display on the statistics page
function updateStatsDisplay() {
    // Ensure stats objects exist for all categories shown in HTML + overall
    const categories = ['overall', 'sectionA', 'sectionB', 'sectionC']; // Match IDs used in HTML
    categories.forEach(prefix => {
        const statsKey = prefix === 'overall' ? 'overall' : prefix.replace('section', ''); // 'A', 'B'
        const statsObj = userData.stats[statsKey] || { totalQuestions: 0, correctAnswers: 0, totalTime: 0 }; // Default if missing

        const totalQ = statsObj.totalQuestions || 0;
        const correctA = statsObj.correctAnswers || 0;
        const totalT = statsObj.totalTime || 0;

        const totalQEl = document.getElementById(`${prefix}TotalQuestionsValue`);
        const correctAEl = document.getElementById(`${prefix}CorrectAnswersValue`);
        const accuracyEl = document.getElementById(`${prefix}AccuracyValue`);
        const avgTimeEl = document.getElementById(`${prefix}AverageTimeValue`);

        if (totalQEl) totalQEl.textContent = totalQ;
        if (correctAEl) correctAEl.textContent = correctA;

        const accuracy = totalQ > 0 ? Math.round((correctA / totalQ) * 100) : 0;
        if (accuracyEl) accuracyEl.textContent = `${accuracy}%`;

        const avgTimeSeconds = totalQ > 0 ? Math.round(totalT / totalQ / 1000) : 0; // Avg time in seconds
        if (avgTimeEl) avgTimeEl.textContent = `${avgTimeSeconds}s`;
    });

    // Update the "Performance by Category" chart
    renderCategoryPerformanceChart();

    // Update recent quizzes list (using the new div)
    const recentQuizzesList = document.getElementById('recentQuizzesList');
    if (recentQuizzesList) {
        recentQuizzesList.innerHTML = ''; // Clear previous list
        const recentQuizzes = userData.quizHistory || []; // Already ordered newest first, limited to 10

        if (recentQuizzes.length === 0) {
            recentQuizzesList.innerHTML = '<p>No quiz history yet.</p>';
        } else {
            recentQuizzes.forEach((quiz, index) => {
                const date = new Date(quiz.date);
                // More robust date formatting
                const formattedDate = date.toLocaleString ? date.toLocaleString(undefined, {
                    year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
                }) : quiz.date.substring(0, 16).replace('T', ' ');

                const listItem = document.createElement('div');
                listItem.classList.add('recent-quiz-item');
                listItem.dataset.historyIndex = index; // Store index to retrieve details later
                listItem.innerHTML = `
                    <span class="quiz-date">${formattedDate}</span>
                    <span class="quiz-mode">${capitalizeFirstLetter(quiz.mode)}</span>
                    <span class="quiz-score">${quiz.correct}/${quiz.questionsCount} (${quiz.accuracy}%)</span>
                    <span class="quiz-time">${formatTime(quiz.time)}</span>
                `;
                // Add click listener to show details
                listItem.addEventListener('click', () => displayQuizHistoryDetail(index));
                recentQuizzesList.appendChild(listItem);
            });
        }
    }
}

// --- Performance by Category Chart Logic ---
let categoryPerformanceChartInstance = null;

function renderCategoryPerformanceChart() {
    const ctx = document.getElementById('categoryPerformanceChart');
    if (!ctx || typeof Chart === 'undefined') return;

    // --- Extract sequential accuracy data for Section A and Section B ---
    // We'll use quizHistory for time-sequenced data (each quiz session)
    // For each quiz, calculate accuracy for Section A and Section B up to that point

    // Prepare arrays for trend lines
    const quizHistory = Array.isArray(userData.quizHistory) ? [...userData.quizHistory].reverse() : [];
    // Reverse so oldest first for time sequence

    // We'll build arrays of {label, accuracyA, accuracyB, accuracyC}
    let trendData = [];
    let cumulativeA = { correct: 0, total: 0 };
    let cumulativeB = { correct: 0, total: 0 };
    let cumulativeC = { correct: 0, total: 0 };

    quizHistory.forEach((quiz, idx) => {
        // For each quiz, count correct/total for A, B, and C in that quiz
        let correctA = 0, totalA = 0, correctB = 0, totalB = 0, correctC = 0, totalC = 0;
        if (Array.isArray(quiz.questions)) {
            quiz.questions.forEach((q, i) => {
                const ans = quiz.answers && quiz.answers[i];
                if (!q || !ans) return;
                if (q.category === 'A') {
                    totalA++;
                    if (ans.isCorrect) correctA++;
                } else if (q.category === 'B') {
                    totalB++;
                    if (ans.isCorrect) correctB++;
                } else if (q.category === 'C') {
                    totalC++;
                    if (ans.isCorrect) correctC++;
                }
            });
        }
        // Update cumulative
        cumulativeA.correct += correctA;
        cumulativeA.total += totalA;
        cumulativeB.correct += correctB;
        cumulativeB.total += totalB;
        cumulativeC.correct += correctC;
        cumulativeC.total += totalC;

        // Use quiz date or index as label
        let label = quiz.date ? (new Date(quiz.date)).toLocaleDateString() : `Quiz ${quizHistory.length - idx}`;
        // If multiple quizzes on same day, add time
        if (quiz.date) {
            const d = new Date(quiz.date);
            label = d.toLocaleDateString() + ' ' + d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        // Calculate cumulative accuracy up to this quiz
        const accuracyA = cumulativeA.total > 0 ? Math.round((cumulativeA.correct / cumulativeA.total) * 100) : null;
        const accuracyB = cumulativeB.total > 0 ? Math.round((cumulativeB.correct / cumulativeB.total) * 100) : null;
        const accuracyC = cumulativeC.total > 0 ? Math.round((cumulativeC.correct / cumulativeC.total) * 100) : null;

        trendData.push({
            label,
            accuracyA,
            accuracyB,
            accuracyC
        });
    });

    // If no quizHistory, fallback to questionHistory (per-question, not time-sequenced)
    if (trendData.length === 0 && userData.questionHistory) {
        // We'll sort questionHistory by timestamp and build a running accuracy
        const entries = Object.entries(userData.questionHistory)
            .map(([qid, entry]) => ({ ...entry, qid }))
            .filter(e => typeof e.timestamp === 'number' && allQuestions)
            .sort((a, b) => a.timestamp - b.timestamp);

        let correctA = 0, totalA = 0, correctB = 0, totalB = 0, correctC = 0, totalC = 0;
        entries.forEach((entry, idx) => {
            const q = allQuestions.find(q => q.id === entry.qid);
            if (!q) return;
            if (q.category === 'A') {
                totalA++;
                if (entry.correct) correctA++;
            } else if (q.category === 'B') {
                totalB++;
                if (entry.correct) correctB++;
            } else if (q.category === 'C') {
                totalC++;
                if (entry.correct) correctC++;
            }
            const label = entry.date ? (new Date(entry.date)).toLocaleDateString() : `Q${idx + 1}`;
            trendData.push({
                label,
                accuracyA: totalA > 0 ? Math.round((correctA / totalA) * 100) : null,
                accuracyB: totalB > 0 ? Math.round((correctB / totalB) * 100) : null,
                accuracyC: totalC > 0 ? Math.round((correctC / totalC) * 100) : null
            });
        });
    }

    // Prepare Chart.js datasets
    const labels = trendData.map(d => d.label);
    const dataA = trendData.map(d => d.accuracyA);
    const dataB = trendData.map(d => d.accuracyB);
    const dataC = trendData.map(d => d.accuracyC);

    // If no data, show empty chart
    if (labels.length === 0) {
        if (categoryPerformanceChartInstance) {
            categoryPerformanceChartInstance.destroy();
            categoryPerformanceChartInstance = null;
        }
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        return;
    }

    // Chart.js config
    const chartConfig = {
        type: 'line',
        data: {
            labels,
            datasets: [
                {
                    label: 'Section A Accuracy (%)',
                    data: dataA,
                    borderColor: 'rgba(52, 152, 219, 1)',
                    backgroundColor: 'rgba(52, 152, 219, 0.15)',
                    fill: false,
                    tension: 0.2,
                    spanGaps: true,
                    pointRadius: 3,
                    pointHoverRadius: 6
                },
                {
                    label: 'Section B Accuracy (%)',
                    data: dataB,
                    borderColor: 'rgba(231, 76, 60, 1)',
                    backgroundColor: 'rgba(231, 76, 60, 0.15)',
                    fill: false,
                    tension: 0.2,
                    spanGaps: true,
                    pointRadius: 3,
                    pointHoverRadius: 6
                },
                {
                    label: 'Section C Accuracy (%)',
                    data: dataC,
                    borderColor: 'rgba(155, 89, 182, 1)',
                    backgroundColor: 'rgba(155, 89, 182, 0.15)',
                    fill: false,
                    tension: 0.2,
                    spanGaps: true,
                    pointRadius: 3,
                    pointHoverRadius: 6
                }
            ]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Accuracy (%)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Quiz Session'
                    },
                    ticks: {
                        maxRotation: 60,
                        minRotation: 0,
                        autoSkip: true,
                        maxTicksLimit: 10
                    }
                }
            }
        }
    };

    // If chart already exists, update it; else, create new
    if (categoryPerformanceChartInstance) {
        categoryPerformanceChartInstance.data.labels = labels;
        categoryPerformanceChartInstance.data.datasets[0].data = dataA;
        categoryPerformanceChartInstance.data.datasets[1].data = dataB;
        categoryPerformanceChartInstance.data.datasets[2].data = dataC;
        categoryPerformanceChartInstance.update();
    } else {
        categoryPerformanceChartInstance = new Chart(ctx, chartConfig);
    }
}


// Check if a question is favorited (uses global userData)
function isFavorite(questionId) {
  // Ensure favoriteQuestions exists
  return userData.favoriteQuestions?.includes(questionId) || false;
}

// Update favorites display on the statistics page (uses global userData)
function updateFavoritesDisplay() {
  const favoritesContainer = document.getElementById('favoriteQuestions');
  if (!favoritesContainer) return;

  const favorites = userData.favoriteQuestions || []; // Use empty array if undefined

  if (favorites.length === 0) {
    favoritesContainer.innerHTML = '<p>You haven\'t saved any favorite questions yet.</p>';
    return;
  }

  favoritesContainer.innerHTML = ''; // Clear previous
  favorites.forEach(questionId => {
    const question = allQuestions.find(q => q.id === questionId);

    if (question) {
       const answerText = Array.isArray(question.answer) ? question.answer.join(', ') : question.answer;
       favoritesContainer.innerHTML += `
         <div class="stat-card">
           <p>${formatQuestionText(question.question).substring(0, 100)}...</p>
           <p><strong>Answer:</strong> ${answerText || 'N/A'}</p>
           <button class="button button-small" onclick="removeFavorite('${questionId}')">Remove</button>
         </div>
       `;
    } else {
       // Handle case where favorited question might be removed from source JSONs later
       favoritesContainer.innerHTML += `
         <div class="stat-card">
           <p>Question ID: ${questionId} (Data not found)</p>
           <button class="button button-small" onclick="removeFavorite('${questionId}')">Remove</button>
         </div>
       `;
    }
  });
}

// Function to remove a favorite (called from button's onclick) (uses global userData)
function removeFavorite(questionId) {
   const nickname = getCurrentNickname();
   if (!nickname) return;

   if (!userData.favoriteQuestions) userData.favoriteQuestions = []; // Ensure array exists

   userData.favoriteQuestions = userData.favoriteQuestions.filter(id => id !== questionId);
   updateFavoritesDisplay(); // Refresh the list
   saveUserData(nickname); // Save changes for the current user
   showFeedback('Favorite removed.');
}

// --- Utility Functions ---

// Show feedback message popup
function showFeedback(message) {
  const existingFeedback = document.querySelector('.feedback-container');
  if (existingFeedback) existingFeedback.remove();

  const feedback = document.createElement('div');
  feedback.classList.add('feedback-container');
  if (document.body.classList.contains('dark-mode')) {
     feedback.classList.add('dark-mode');
  }
  feedback.textContent = message; // Use textContent for safety

  document.body.appendChild(feedback);

  // Fade out and remove after a delay
  setTimeout(() => {
    feedback.style.transition = 'opacity 0.3s ease-out';
    feedback.style.opacity = '0';
    setTimeout(() => {
      feedback.remove();
    }, 300);
  }, 3000);
}

// Toggle hint visibility
function toggleHint() {
  if (!hintContentElement || !hintToggleBtnElement) return;

  const isHidden = hintContentElement.classList.toggle('hidden');
  hintToggleBtnElement.textContent = isHidden ? 'Show Hint' : 'Hide Hint';
}

// Create confetti effect for correct answers/good scores
function createConfetti() {
  const confettiContainer = document.createElement('div');
  confettiContainer.classList.add('confetti');
  document.body.appendChild(confettiContainer);

  const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800', '#FF5722'];

  for (let i = 0; i < 100; i++) {
    const confetti = document.createElement('div');
    // Styles remain the same...
    confetti.style.position = 'absolute';
    confetti.style.width = `${Math.random() * 10 + 5}px`;
    confetti.style.height = `${Math.random() * 10 + 5}px`;
    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
    confetti.style.left = `${Math.random() * 100}%`;
    confetti.style.top = `-10px`;
    confetti.style.opacity = `${Math.random() * 0.8 + 0.2}`;
    confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
    confetti.style.borderRadius = `${Math.random() > 0.5 ? '50%' : '0'}`;

    confettiContainer.appendChild(confetti);

    const duration = Math.random() * 3 + 2;
    const delay = Math.random() * 2;

    confetti.animate([
      { transform: `translateY(0) rotate(0deg)`, opacity: 1 },
      { transform: `translateY(${window.innerHeight + 10}px) rotate(${Math.random() * 720}deg)`, opacity: 0 }
    ], {
      duration: duration * 1000,
      delay: delay * 1000,
      easing: 'ease-out',
      fill: 'forwards'
    });
  }

  setTimeout(() => {
    confettiContainer.remove(); // Use remove()
  }, 5000);
}

// Helper function to capitalize first letter
function capitalizeFirstLetter(string) {
  if (!string) return '';
  if (string === 'multiple choice') return 'Multiple Choice';
  return string.charAt(0).toUpperCase() + string.slice(1);
}

// --- Data Loading ---

// Load JSON questions from files with enhanced error handling
function fetchQuestions() {
  console.log("Fetching questions and acronyms...");

  // Log the current working directory to help with debugging
  console.log("Current working directory (expected):", window.location.href);

  const fetchPromises = [
    // Fetch existing files (assuming they contain flat arrays)
    fetch('output2.json').then(response => {
      console.log("output2.json response status:", response.status);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for output2.json`);
      return response.json();
    }).then(data => {
      console.log("output2.json loaded successfully with", Array.isArray(data) ? data.length : "non-array", "items");
      return data;
    }).catch(e => { console.error("Error fetching/parsing output2.json:", e); return []; }), // Return empty array on error

    fetch('questions.json').then(response => {
      console.log("questions.json response status:", response.status);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for questions.json`);
      return response.json();
    }).then(data => {
      console.log("questions.json loaded successfully with", Array.isArray(data) ? data.length : "non-array", "items");
      return data;
    }).catch(e => { console.error("Error fetching/parsing questions.json:", e); return []; }), // Return empty array on error

    // Fetch the new performance questions file (expecting an array)
    fetch('performance_questions.json').then(response => {
      console.log("performance_questions.json response status:", response.status);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for performance_questions.json`);
      return response.json();
    }).then(data => {
      console.log("performance_questions.json loaded successfully with", Array.isArray(data) ? data.length : "non-array", "items");
      return data;
    }).catch(e => { console.error("Error fetching/parsing performance_questions.json:", e); return []; }), // Return empty array on error

    // Fetch the acronyms file
    fetch('acronyms.json').then(response => {
      console.log("acronyms.json response status:", response.status);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for acronyms.json`);
      return response.json();
    }).then(data => {
      console.log("acronyms.json loaded successfully with", Array.isArray(data) ? data.length : "non-array", "items");
      return data;
    }).catch(e => { console.error("Error fetching/parsing acronyms.json:", e); return []; }), // Return empty array on error

    // Fetch the intrebari.json file for Section C questions
    fetch('intrebari.json').then(response => {
      console.log("intrebari.json response status:", response.status);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for intrebari.json`);
      return response.json();
    }).then(data => {
      console.log("intrebari.json loaded successfully with", Array.isArray(data) ? data.length : "non-array", "items");
      return data;
    }).catch(e => { console.error("Error fetching/parsing intrebari.json:", e); return []; }) // Return empty array on error
  ];

  return Promise.all(fetchPromises)
  .then(results => {
    // results will be an array like [output2Data, questionsData, performanceData, acronymData, intrebariData]
    const [output2Data, questionsData, performanceData, acronymData, intrebariData] = results;
    let allProcessedQuestions = [];
    let loadedSuccessfully = false; // Track if *any* data was loaded

    // Helper to process a flat array of questions (like from old files)
    const processFlatArray = (questions, category) => {
       if (!questions || !Array.isArray(questions)) {
           console.warn(`Invalid or missing data for flat array category ${category}.`);
           return [];
       }
       loadedSuccessfully = true;
       return questions.map((q, index) => {
          const isMultiple = q['multiple answers'] && q['multiple answers'] > 1;
          const answer = (isMultiple && !Array.isArray(q.answer)) ? [q.answer] : q.answer;
          const difficulty = isMultiple ? 'multiple choice' : (q.difficulty || 'medium');
          let stableId = `q_${category}_${index}`;
          if (q.question) {
              if (category === 'A') {
                  const match = q.question.match(/^A\d{1,2}/);
                  if (match) stableId = match[0];
              } else if (category === 'B') {
                  const match = q.question.match(/^Chapter-\w+-Question-\d+/);
                  if (match) stableId = match[0];
              }
          }
          const finalId = q.id || stableId;
          return { ...q, answer, category, id: finalId, difficulty };
       });
    };

    // Helper to process a single performance question object (now handles different types and takes category/index)
    const processSinglePerformanceQuestion = (questionData, category, index) => {
        if (!questionData || typeof questionData !== 'object' || questionData.error) {
             console.warn(`Invalid or missing data for performance question at index ${index}.`);
             return null; // Return null if data is invalid
        }

        // Validate required fields based on question type
        if (questionData.type === 'matching' && (!questionData.availableControls || !questionData.locations || !questionData.correctMatches)) {
            console.warn(`Matching question at index ${index} is missing required fields (availableControls, locations, or correctMatches)`);
            // Don't return null, try to process anyway
        } else if (questionData.type === 'matching_categories' && (!questionData.categories || !questionData.scenarios || !questionData.correctMatches)) {
            console.warn(`Matching categories question at index ${index} is missing required fields (categories, scenarios, or correctMatches)`);
            // Don't return null, try to process anyway
        } else if (questionData.type === 'ordering' && (!questionData.items || !questionData.correctOrder)) {
            console.warn(`Ordering question at index ${index} is missing required fields (items or correctOrder)`);
            // Don't return null, try to process anyway
        }

        // Assign default/necessary properties based on type
        let difficulty = 'performance'; // Default for performance types
        if (questionData.type !== 'matching' && questionData.type !== 'matching_categories' && questionData.type !== 'ordering') {
            difficulty = questionData.difficulty || 'medium'; // Use specified or medium for non-performance
        }

        const finalId = questionData.id || `perf_${category}_${index}`; // Generate an ID using index

        // Create a deep copy of the question data to avoid reference issues
        const processedQuestion = {
            ...JSON.parse(JSON.stringify(questionData)),
            id: finalId,
            difficulty: difficulty, // Assign difficulty for filtering
            category: category, // Assign a category (e.g., 'P' for Performance)
            sectionTitle: questionData.sectionTitle || 'Performance based' // Assign section title if needed elsewhere
        };

        console.log(`Processed performance question ${finalId} of type ${questionData.type}`);
        return processedQuestion;
    };

    // Process results, directly using the data or empty array
    const sectionBQuestions = processFlatArray(output2Data, 'B');
    const sectionAQuestions = processFlatArray(questionsData, 'A');
    let performanceQuestions = []; // Initialize as an array

    // Process the performance questions result (expecting an array)
    if (Array.isArray(performanceData)) { // Check if performanceData is an array (not [])
        console.log("Processing performance questions. Found:", performanceData.length);
        performanceData.forEach((qData, index) => { // Iterate over performanceData
            // Process specific performance types - include all types for now
            if (qData && qData.type) {
                console.log(`Processing performance question ${index} of type: ${qData.type}`);

                // Make sure the question has all required fields
                if (qData.type === 'matching' && (!qData.availableControls || !qData.locations || !qData.correctMatches)) {
                    console.warn(`Matching question at index ${index} is missing required fields (availableControls, locations, or correctMatches)`);
                    console.log("Question data:", JSON.stringify(qData));
                } else if (qData.type === 'matching_categories' && (!qData.categories || !qData.scenarios || !qData.correctMatches)) {
                    console.warn(`Matching categories question at index ${index} is missing required fields (categories, scenarios, or correctMatches)`);
                    console.log("Question data:", JSON.stringify(qData));
                } else if (qData.type === 'ordering' && (!qData.items || !qData.correctOrder)) {
                    console.warn(`Ordering question at index ${index} is missing required fields (items or correctOrder)`);
                    console.log("Question data:", JSON.stringify(qData));
                }

                const processedQ = processSinglePerformanceQuestion(qData, 'P', index); // Pass category 'P' and index
                if (processedQ) {
                    performanceQuestions.push(processedQ);
                    console.log(`Successfully processed performance question: ${processedQ.id}`);
                } else {
                    console.warn(`Skipping invalid performance question object at index ${index} in performance_questions.json`);
                }
            } else {
                console.warn(`Skipping question with missing type at index ${index} in performance_questions.json`);
            }
        });
        console.log(`Total performance questions processed: ${performanceQuestions.length}`);
        if (performanceQuestions.length > 0) {
             // Check if any data was loaded successfully from any source
             if (sectionAQuestions.length > 0 || sectionBQuestions.length > 0 || performanceQuestions.length > 0) {
                 loadedSuccessfully = true;
                 console.log("Questions loaded successfully. Performance questions:", performanceQuestions.length);
             }
        }
    } else {
        console.warn("performance_questions.json did not contain a valid array or failed to load.");
    updateSectionProgressBars(); // Update section progress on initial load

    }

    // Process Section C questions from intrebari.json
    let sectionCQuestions = [];
    if (Array.isArray(intrebariData) && intrebariData.length > 0) {
        sectionCQuestions = intrebariData.map((q, index) => {
            const finalId = q.id || `q_C_${index}`;
            return {
                ...q,
                id: finalId,
                category: 'C', // Assign to category C
                difficulty: q.difficulty || 'medium', // Default to medium difficulty if not specified
                sectionTitle: 'Section C' // Add section title for display
            };
        });
        console.log(`Loaded ${sectionCQuestions.length} questions for Section C from intrebari.json`);
        if (sectionCQuestions.length > 0) {
            loadedSuccessfully = true;
        }
    } else {
        console.warn("intrebari.json did not contain a valid array or failed to load.");
    }

    // Combine all processed questions
    allQuestions = [...sectionAQuestions, ...sectionBQuestions, ...performanceQuestions, ...sectionCQuestions]; // Add all question arrays

    // Process Acronyms
    if (Array.isArray(acronymData) && acronymData.length > 0) {
        allAcronyms = acronymData;
        console.log(`Loaded ${allAcronyms.length} acronyms.`);
        loadedSuccessfully = true;
    } else {
        console.warn("acronyms.json did not contain a valid array or failed to load.");
        allAcronyms = []; // Ensure it's an empty array if loading failed
    }


    // Check if *any* questions were loaded successfully
    if (allQuestions.length === 0) {
        // If no questions loaded, always fall back to mock data
        console.warn("No questions loaded successfully. Falling back to mock data.");
        showFeedback('Using mock questions for this session.');
        allQuestions = mockQuestions.map((q, index) => ({
            ...q,
            category: q.category || (q.id.startsWith('A') ? 'A' : q.id.startsWith('B') ? 'B' : 'C'),
            id: q.id || `mock_${index}`
        }));

        // Log the mock questions being used
        console.log("Using mock questions:", allQuestions);
    }

    // Check if acronyms were loaded
    if (allAcronyms.length === 0) {
        // Create some mock acronyms if none were loaded
        console.warn("No acronyms loaded. Creating mock acronyms.");
        allAcronyms = [
            { acronym: "HTML", spelledOut: "HyperText Markup Language" },
            { acronym: "CSS", spelledOut: "Cascading Style Sheets" },
            { acronym: "JS", spelledOut: "JavaScript" },
            { acronym: "API", spelledOut: "Application Programming Interface" },
            { acronym: "HTTP", spelledOut: "HyperText Transfer Protocol" },
            { acronym: "URL", spelledOut: "Uniform Resource Locator" },
            { acronym: "JSON", spelledOut: "JavaScript Object Notation" },
            { acronym: "DOM", spelledOut: "Document Object Model" },
            { acronym: "SQL", spelledOut: "Structured Query Language" },
            { acronym: "XML", spelledOut: "eXtensible Markup Language" }
        ];
        console.log("Using mock acronyms:", allAcronyms);
    }

    console.log('Final questions count:', allQuestions.length);
    console.log('Final acronyms count:', allAcronyms.length);
    handleFilterChange(); // Update UI based on final question list
    return { allQuestions, allAcronyms }; // Return both
  });
}

// --- Initialization ---

// Initialize the application
function init() {
  loadAndDisplayNickname(); // Checks for nickname, loads/initializes data, updates UI
  fetchQuestions().then(() => {
    // Update progress bars *after* questions are fetched and potentially after user data is loaded
    updateDailyProgressBars();
  });
  addClearDataButton(); // Add the reset button (its logic now depends on current user)

  // Initial UI setup is now handled within loadAndDisplayNickname and saveNickname
  // updateDailyProgressBars() is called within loadUserData and fetchQuestions callback
  // based on whether a nickname exists and data is loaded.
}

// Add filter change handler (updates available question count display)
function handleFilterChange() {
  const categoryFilter = document.querySelector('.categories-filter .active')?.dataset.category || 'all';
  const difficultyFilter = document.querySelector('.difficulty-filter .active')?.dataset.difficulty || 'all';
  const customCount = questionCountInput ? (parseInt(questionCountInput.value) || 10) : 10;

  // Count available questions based on current filters (excluding acronyms for now)
  const availableQuestions = getFilteredQuestions().length; // Use helper

  // Update UI feedback and input max value
  const difficultyText = difficultyFilter !== 'all' ? ` (${capitalizeFirstLetter(difficultyFilter)} difficulty)` : '';
  const feedbackMessage = `${availableQuestions} questions available for ${categoryFilter !== 'all' ? 'Section ' + categoryFilter : 'All Sections'}${difficultyText}`;

  if (questionCountInput) {
      // Adjust max based on whether filters are active (use total if 'all')
      const maxCount = (categoryFilter === 'all' && difficultyFilter === 'all') ? allQuestions.length : availableQuestions;
      questionCountInput.max = Math.max(1, maxCount); // Ensure max is at least 1

      if (customCount > availableQuestions && categoryFilter !== 'all' && difficultyFilter !== 'all' && availableQuestions > 0) {
          showFeedback(`Warning: Only ${availableQuestions} questions available for selected filters. Requested ${customCount}.`);
          // Optionally adjust the input value: questionCountInput.value = availableQuestions;
      } else if (availableQuestions === 0 && (categoryFilter !== 'all' || difficultyFilter !== 'all')) {
          showFeedback(`No questions available for the selected filters.`);
          // Optionally disable start buttons or modes
      } else {
          showFeedback(feedbackMessage); // Show available count
      }
  } else {
      // Fallback if input doesn't exist
      if (availableQuestions === 0 && (categoryFilter !== 'all' || difficultyFilter !== 'all')) {
          showFeedback(`No questions available for the selected filters.`);
      } else {
          showFeedback(feedbackMessage);
      }
  }
}

// --- Select Question Mode Specific Functions ---

// Display the list of questions for selection
function displayQuestionSelectionList(questions) {
  const listContainer = document.getElementById('questionSelectionList');
  if (!listContainer) return;

  listContainer.innerHTML = ''; // Clear previous list

  if (questions.length === 0) {
    listContainer.innerHTML = '<p>No questions match the current filters.</p>';
    return;
  }

  questions.forEach(question => {
    const questionDiv = document.createElement('div');
    questionDiv.classList.add('question-list-item');
    // Display first 100 chars of question text
    // Use questionText if available (for matching/ordering), otherwise use question
    const text = question.questionText || question.question || '';
    const questionSnippet = formatQuestionText(text).substring(0, 100);
    questionDiv.innerHTML = `
      <span class="question-snippet">${questionSnippet}...</span>
      <button class="button button-small select-question-btn" data-question-id="${question.id}">Select</button>
    `;
    listContainer.appendChild(questionDiv);
  });
}

// Start the quiz with a single selected question
function startSingleQuestionQuiz(questionId) {
  const question = allQuestions.find(q => q.id === questionId);
  if (!question) {
    showFeedback('Selected question not found.');
    return;
  }

  currentMode = 'select'; // Set mode explicitly
  quizSettings = getQuizSettingsForMode('select'); // Get settings for select mode

  // Reset the quiz container HTML structure first
  resetQuizContainer();
  cacheDOMElements(currentMode); // Cache elements after rebuilding, passing the mode

  // Set up the quiz state for this single question
  currentQuestions = [question]; // Only this question
  currentQuestionIndex = 0;
  correctAnswers = 0; // Reset score for this single attempt
  quizStartTime = Date.now();
  currentQuizAnswers = {}; // Reset answers

  // Display the question
  displayQuestion();

  // Switch view
  showView('quiz');
  if(homeButton) homeButton.classList.remove('active');
  if(statsButton) statsButton.classList.remove('active');
}

// Add a "Back to List" button to the quiz container in select mode
function addBackToListButton() {
    const buttonsContainer = quizContainer.querySelector('.buttons');
    if (!buttonsContainer || buttonsContainer.querySelector('#backToListBtn')) return; // Don't add if exists

    // Remove the 'Next Question' button if it exists
    const nextBtn = document.getElementById('nextQuestionBtn');
    if (nextBtn) nextBtn.remove();

    const backButton = document.createElement('button');
    backButton.id = 'backToListBtn';
    backButton.className = 'button button-secondary'; // Use secondary style
    backButton.textContent = 'Back to Question List';
    backButton.disabled = true; // Disabled until answer is checked
    backButton.addEventListener('click', () => {
        // Go back to the selection list view, preserving filters
        const filteredQuestions = getFilteredQuestions();
        displayQuestionSelectionList(filteredQuestions);
        showView('select');
    });
    buttonsContainer.appendChild(backButton);
}

// Add a "Next Acronym" button for acronym mode
function addNextAcronymButton() {
    const buttonsContainer = quizContainer.querySelector('.buttons');
    if (!buttonsContainer) return;

    // Ensure the standard 'Next Question' button is used and relabeled
    const nextBtn = document.getElementById('nextQuestionBtn');
    if (nextBtn) {
        nextBtn.textContent = 'Next Acronym';
        nextBtn.disabled = true; // Disabled until answer is shown
        // Ensure listener is attached (cacheQuizDOMElements should handle this if called after reset)
        nextBtn.removeEventListener('click', nextQuestion); // Remove old listener if any
        nextBtn.addEventListener('click', nextQuestion); // Add listener for acronym mode
    } else {
        console.error("Could not find nextQuestionBtn to relabel for acronyms.");
    }
}


// --- Quiz History Detail View ---

// Function to display the details of a specific past quiz
function displayQuizHistoryDetail(index) {
    const historyEntry = userData.quizHistory[index];
    if (!historyEntry) {
        showFeedback("Could not find the selected quiz history.");
        return;
    }

    const detailContainer = document.getElementById('quizHistoryDetailContainer');
    const detailContent = document.getElementById('quizHistoryDetailContent');
    const statsViewContainer = document.getElementById('statsContainer'); // Get stats container

    if (!detailContainer || !detailContent || !statsViewContainer) {
        console.error("History detail or stats container elements not found.");
        return;
    }

    detailContent.innerHTML = ''; // Clear previous details

    // Add overall quiz info
    const date = new Date(historyEntry.date);
    const formattedDate = date.toLocaleString ? date.toLocaleString(undefined, {
        year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
    }) : historyEntry.date.substring(0, 16).replace('T', ' ');

    const summaryDiv = document.createElement('div');
    summaryDiv.classList.add('quiz-history-summary');
    summaryDiv.innerHTML = `
        <p><strong>Mode:</strong> ${capitalizeFirstLetter(historyEntry.mode)}</p>
        <p><strong>Date:</strong> ${formattedDate}</p>
        <p><strong>Score:</strong> ${historyEntry.correct}/${historyEntry.questionsCount} (${historyEntry.accuracy}%)</p>
        <p><strong>Time:</strong> ${formatTime(historyEntry.time)}</p>
        <hr>
        <h4>Questions & Answers:</h4>
    `;
    detailContent.appendChild(summaryDiv);

    // Loop through questions in the history entry
    historyEntry.questions.forEach((question, qIndex) => {
        const answerData = historyEntry.answers[qIndex] || { userAnswer: 'Not answered', isCorrect: false };
        const isMultipleAnswer = Array.isArray(question.answer);
        let userAnswerText = 'Not Answered';
        let correctAnswerDisplay = '';

        // Format user answer and correct answer based on type (similar to populateQuestionReport)
        if (question.type === 'matching') {
            if (typeof answerData.userAnswer === 'object' && answerData.userAnswer !== null && answerData.userAnswer !== 'Not Answered') {
                userAnswerText = Object.entries(answerData.userAnswer)
                    .map(([locId, controls]) => {
                        const loc = question.locations?.find(l => l.id === locId); // Add optional chaining
                        const controlsText = Array.isArray(controls) ? controls.join(', ') : 'None';
                        return `${loc ? loc.name : locId}: ${controlsText || 'None'}`;
                    })
                    .join('<br>');
            }
            if (!answerData.isCorrect && question.correctMatches) { // Check correctMatches exists
                 correctAnswerDisplay = '<p style="color: var(--success-color); font-weight: bold;">Correct matches:<br>';
                 Object.entries(question.correctMatches).forEach(([locId, controls]) => {
                     const loc = question.locations?.find(l => l.id === locId); // Add optional chaining
                     correctAnswerDisplay += `${loc ? loc.name : locId}: ${controls.join(', ')}<br>`;
                 });
                 correctAnswerDisplay += '</p>';
            }
        } else if (question.type === 'matching_categories') {
             if (typeof answerData.userAnswer === 'object' && answerData.userAnswer !== null && answerData.userAnswer !== 'Not Answered') {
                 const answersByCategory = {};
                 Object.entries(answerData.userAnswer).forEach(([scenarioId, categoryName]) => {
                     if (!answersByCategory[categoryName]) answersByCategory[categoryName] = [];
                     const scenario = question.scenarios?.find(s => s.id === scenarioId); // Add optional chaining
                     answersByCategory[categoryName].push(scenario ? scenario.text : scenarioId);
                 });
                 userAnswerText = Object.entries(answersByCategory)
                     .map(([categoryName, scenarios]) => `<strong>${categoryName}:</strong><br> - ${scenarios.join('<br> - ')}`)
                     .join('<br><br>');
             }
             if (!answerData.isCorrect && question.correctMatches) { // Check correctMatches exists
                 correctAnswerDisplay = '<p style="color: var(--success-color); font-weight: bold;">Correct matches:<br>';
                 Object.entries(question.correctMatches).forEach(([scenarioId, categoryName]) => {
                     const scenario = question.scenarios?.find(s => s.id === scenarioId); // Add optional chaining
                     correctAnswerDisplay += `${scenario ? scenario.text : scenarioId}: ${categoryName}<br>`;
                 });
                 correctAnswerDisplay += '</p>';
             }
        } else if (question.type === 'ordering') {
            if (Array.isArray(answerData.userAnswer) && answerData.userAnswer.length > 0) {
                userAnswerText = answerData.userAnswer.map((item, i) => `${i + 1}. ${item}`).join('<br>');
            }
            if (!answerData.isCorrect && question.correctOrder) { // Check correctOrder exists
                correctAnswerDisplay = '<p style="color: var(--success-color); font-weight: bold;">Correct order:<br>';
                correctAnswerDisplay += question.correctOrder.map((item, i) => `${i + 1}. ${item}`).join('<br>');
                correctAnswerDisplay += '</p>';
            }
        } else { // Radio/Checkbox
             userAnswerText = Array.isArray(answerData.userAnswer) ? answerData.userAnswer.join(', ') : answerData.userAnswer;
             if (!answerData.isCorrect) {
                 const correctAnswerText = isMultipleAnswer ? (question.answer || []).join(', ') : question.answer; // Handle undefined answer
                 correctAnswerDisplay = `<p style="color: var(--success-color); font-weight: bold;">Correct answer${isMultipleAnswer ? 's' : ''}: ${correctAnswerText || 'N/A'}</p>`;
             }
        }

        // Create DOM Elements for the question detail
        const resultDiv = document.createElement('div');
        resultDiv.className = 'question-result'; // Reuse class from results screen
        resultDiv.style.margin = '10px 0';
        resultDiv.style.padding = '15px';
        resultDiv.style.borderRadius = '8px';
        resultDiv.style.backgroundColor = answerData.isCorrect ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)';
        resultDiv.style.borderLeft = `4px solid ${answerData.isCorrect ? 'var(--success-color)' : 'var(--danger-color)'}`;

        const questionP = document.createElement('p');
        const questionTextKey = (question.type === 'matching' || question.type === 'matching_categories' || question.type === 'ordering') ? 'questionText' : 'question';
        questionP.innerHTML = `<strong>Question ${qIndex + 1}:</strong> ${formatQuestionText(question[questionTextKey])}`;
        resultDiv.appendChild(questionP);

        const userAnswerP = document.createElement('p');
        userAnswerP.style.color = answerData.isCorrect ? 'var(--success-color)' : 'var(--danger-color)';
        userAnswerP.style.fontWeight = 'bold';
        userAnswerP.innerHTML = `Your answer: ${userAnswerText}`;
        resultDiv.appendChild(userAnswerP);

        if (correctAnswerDisplay) {
            const correctAnswerDiv = document.createElement('div');
            correctAnswerDiv.innerHTML = correctAnswerDisplay;
            resultDiv.appendChild(correctAnswerDiv);
        }

        // Add explanation if available in the question data
        if (question.explanation) {
            const explanationDiv = document.createElement('div');
            explanationDiv.className = 'explanation-content'; // Show by default in history
            explanationDiv.style.marginTop = '10px';
            explanationDiv.style.padding = '10px';
            explanationDiv.style.backgroundColor = 'rgba(0,0,0,0.05)';
            explanationDiv.style.borderRadius = '4px';
            explanationDiv.innerHTML = `<strong>Explanation:</strong><br>${formatExplanation(question.explanation)}`;
            resultDiv.appendChild(explanationDiv);
        } else if (question.type === 'matching' && question.locations?.some(loc => loc.explanation)) {
             const explanationHTML = question.locations
                .filter(loc => !!loc.explanation)
                .map(loc => `<strong>${loc.name}:</strong><br>${formatExplanation(loc.explanation)}`)
                .join('<hr style="margin: 8px 0; border: none; border-top: 1px dashed var(--border-color);">');
             const explanationDiv = document.createElement('div');
             explanationDiv.className = 'explanation-content';
             explanationDiv.style.marginTop = '10px';
             explanationDiv.style.padding = '10px';
             explanationDiv.style.backgroundColor = 'rgba(0,0,0,0.05)';
             explanationDiv.style.borderRadius = '4px';
             explanationDiv.innerHTML = `<strong>Explanations:</strong><br>${explanationHTML}`;
             resultDiv.appendChild(explanationDiv);
        } else if (question.type === 'matching_categories' && question.explanations) {
             const explanationHTML = Object.entries(question.explanations)
                 .map(([catName, text]) => `<strong>${catName}:</strong><br>${formatExplanation(text)}`)
                 .join('<hr style="margin: 8px 0; border: none; border-top: 1px dashed var(--border-color);">');
             const explanationDiv = document.createElement('div');
             explanationDiv.className = 'explanation-content';
             explanationDiv.style.marginTop = '10px';
             explanationDiv.style.padding = '10px';
             explanationDiv.style.backgroundColor = 'rgba(0,0,0,0.05)';
             explanationDiv.style.borderRadius = '4px';
             explanationDiv.innerHTML = `<strong>Explanations:</strong><br>${explanationHTML}`;
             resultDiv.appendChild(explanationDiv);
        }


        detailContent.appendChild(resultDiv);
    });

    // Show the detail container and hide the stats container
    statsViewContainer.classList.add('hidden');
    detailContainer.classList.remove('hidden');
    detailContainer.scrollIntoView({ behavior: 'smooth' }); // Scroll to the detail view
}

// Function to close the history detail view
function closeQuizHistoryDetail() {
    const detailContainer = document.getElementById('quizHistoryDetailContainer');
    const statsViewContainer = document.getElementById('statsContainer');
    if (detailContainer && statsViewContainer) {
        detailContainer.classList.add('hidden');
        statsViewContainer.classList.remove('hidden');
    }
}

// Add listener for the close button in the history detail view
document.addEventListener('DOMContentLoaded', () => {
    const closeBtn = document.getElementById('closeHistoryDetailBtn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeQuizHistoryDetail);
    }
    // Other listeners... (init call is already here)
});


// --- Acronym Learning Mode Functions ---

let currentAcronymSet = []; // Holds the current 10 acronyms being displayed
let acronymIndex = 0; // Tracks the starting index for the next set
let acronymRevealListenerAttached = false; // Flag to prevent multiple listeners

// Loads the next set of 10 acronyms
function loadAcronymSet() {
    if (!allAcronyms || allAcronyms.length === 0) {
        showFeedback('Acronym data not loaded.');
        return false;
    }
    const count = 3; // Display 3 acronyms at a time for 3x1 grid
    // Shuffle all acronyms initially or when restarting
    if (acronymIndex === 0) {
        allAcronyms = shuffleArray([...allAcronyms]);
    }

    // Get the next slice, handle wrapping around
    const remaining = allAcronyms.length - acronymIndex;
    if (remaining < count && acronymIndex !== 0) { // Check index !== 0 to avoid wrapping on first load if less than 10 total
        // Take remaining and wrap around
        currentAcronymSet = allAcronyms.slice(acronymIndex).concat(allAcronyms.slice(0, count - remaining));
        acronymIndex = count - remaining; // Update index for next time
        showFeedback("Reached end of list, wrapping around.");
    } else {
        currentAcronymSet = allAcronyms.slice(acronymIndex, acronymIndex + count);
        acronymIndex += count;
    }

    // Reset index if it reaches the end exactly
    if (acronymIndex >= allAcronyms.length) {
        acronymIndex = 0;
    }

    console.log("Loaded acronym set:", currentAcronymSet);
    return true;
}

// Displays the current set of acronyms in the grid (Learning Mode) - Reverted Structure
function displayAcronyms() {
    if (!acronymsGridElement) {
        console.error("Acronyms grid element not found.");
        return;
    }

    // Clear previous grid
    acronymsGridElement.innerHTML = '';

    // Clear any existing AI result container
    const aiResultContainer = document.getElementById('aiResultContainer');
    if (aiResultContainer) {
        aiResultContainer.remove();
    }

    currentAcronymSet.forEach((acronymData, index) => {
        const card = document.createElement('div');
        card.classList.add('acronym-card'); // Main card container
        card.dataset.acronym = acronymData.acronym; // Store acronym

        const title = document.createElement('h4');
        title.classList.add('acronym-title'); // Class for styling
        title.textContent = acronymData.acronym;

        const revealButton = document.createElement('button');
        revealButton.classList.add('button', 'button-small', 'reveal-acronym-btn');
        revealButton.innerHTML = '<i class="fa-solid fa-eye"></i>'; // Eye icon
        revealButton.setAttribute('aria-label', `Reveal meaning for ${acronymData.acronym}`);
        revealButton.dataset.index = index; // Link button to definition index

        const definitionDiv = document.createElement('div');
        definitionDiv.classList.add('acronym-definition', 'hidden'); // Hidden by default
        definitionDiv.id = `acronym-definition-${index}`; // Unique ID

        // Format the explanation with proper structure
        const formattedContent = formatAcronymExplanation(acronymData.spelledOut, acronymData.acronym);
        definitionDiv.innerHTML = formattedContent;

        // Append elements in the desired order (definition INSIDE card)
        card.appendChild(title);
        card.appendChild(revealButton);
        card.appendChild(definitionDiv);

        acronymsGridElement.appendChild(card);
    });

    // Add event listener for reveal buttons using event delegation on the grid
    if (acronymsGridElement) { // Check if grid element exists
        // Remove previous listener if it exists by cloning/replacing the node
        const newGrid = acronymsGridElement.cloneNode(true); // Clone to remove listeners
        acronymsGridElement.parentNode.replaceChild(newGrid, acronymsGridElement); // Replace old with new
        acronymsGridElement = newGrid; // Update the reference to the new grid

        acronymsGridElement.addEventListener('click', (event) => {
            const revealBtn = event.target.closest('.reveal-acronym-btn');
            if (revealBtn) {
                const index = revealBtn.dataset.index; // Use index again
                const definitionDiv = document.getElementById(`acronym-definition-${index}`); // Find by index ID
                if (definitionDiv) {
                    const wasHidden = definitionDiv.classList.toggle('hidden');
                    // Change icon based on visibility
                    revealBtn.innerHTML = wasHidden
                        ? '<i class="fa-solid fa-eye-slash"></i>' // Show slash when visible
                        : '<i class="fa-solid fa-eye"></i>'; // Show eye when hidden

                    // Keep AI Features button visible regardless of definition visibility
                    console.log(`Reveal toggled. Definition now visible: ${!wasHidden}. aiFeaturesBtnElement exists: ${!!aiFeaturesBtnElement}`); // Debug log
                    if (aiFeaturesBtnElement) {
                        console.log("Ensuring AI button is visible"); // Debug log
                        // Force visibility styles
                        aiFeaturesBtnElement.classList.remove('ai-button-hidden');
                        aiFeaturesBtnElement.style.display = 'inline-block'; // Or 'block' if preferred
                        aiFeaturesBtnElement.style.visibility = 'visible';
                        aiFeaturesBtnElement.style.position = 'static'; // Ensure it's not positioned off-screen
                        console.log("AI Features button visible. Class list:", aiFeaturesBtnElement.classList, "Styles:", aiFeaturesBtnElement.style.cssText);
                    }
                } else {
                     console.error(`Definition div with ID acronym-definition-${index} not found.`); // Keep error log
                }
            }
        });
        // Removed acronymRevealListenerAttached flag and related if condition
    }

    // Reset button states - Enable Next immediately in learning mode
    if (nextAcronymsBtnElement) nextAcronymsBtnElement.disabled = false; // Enable Next Set button

    // Add AI Tools button to the acronyms container if it doesn't exist
    let aiToolsBtn = document.querySelector('.acronyms-container .buttons #aiFeaturesBtn');
    if (!aiToolsBtn) {
        // Create a new AI Tools button
        aiToolsBtn = document.createElement('button');
        aiToolsBtn.id = 'aiFeaturesBtn';
        aiToolsBtn.className = 'button';
        aiToolsBtn.innerHTML = '<i class="fa-solid fa-robot"></i> AI Tools';
        aiToolsBtn.setAttribute('aria-label', 'AI Features');

        // Add the button to the buttons container
        const buttonsContainer = document.querySelector('.acronyms-container .buttons');
        if (buttonsContainer) {
            // Insert before the Return Home button
            const returnHomeBtn = document.getElementById('returnHomeFromAcronymsBtn');
            if (returnHomeBtn) {
                buttonsContainer.insertBefore(aiToolsBtn, returnHomeBtn);
            } else {
                buttonsContainer.appendChild(aiToolsBtn);
            }
        }
    }

    // Make sure the button is visible and has a click handler
    if (aiToolsBtn) {
        aiToolsBtn.classList.remove('ai-button-hidden');
        aiToolsBtn.classList.remove('hidden');
        aiToolsBtn.style.display = 'inline-block';
        aiToolsBtn.style.visibility = 'visible';
        aiToolsBtn.style.position = 'static';

        // Ensure it has a click handler
        aiToolsBtn.onclick = function() {
            console.log("AI button clicked from displayAcronyms handler");
            handleAiFeaturesClick();
            return false;
        };

        // Update the global reference
        aiFeaturesBtnElement = aiToolsBtn;
    } else {
        console.error("AI button not found and could not be created in displayAcronyms");
    }
}

// Removed checkAcronymAnswers function as it's no longer needed

// Loads and displays the next set of acronyms
function loadNextAcronymSet() {
    // Clear any existing AI result container
    const aiResultContainer = document.getElementById('aiResultContainer');
    if (aiResultContainer) {
        aiResultContainer.remove();
    }

    // Reset the window.allAcronymsData to ensure it's refreshed for the new set
    window.allAcronymsData = null;

    if (loadAcronymSet()) {
        displayAcronyms(); // This will now enable the next button itself
    } else {
        // Handle case where loading fails (e.g., data disappeared)
        showFeedback("Failed to load next set of acronyms.");
        showView('home');
    }
}


// --- AI Question Generation (Gemini) ---

async function generateAiQuestion(topic = 'CompTIA Security+ certification', difficulty = 'medium') {
    console.log("Attempting to generate AI question...");
    const apiKey = localStorage.getItem('geminiApiKey');

    if (!apiKey) {
        console.log("API Key check failed: No key found in localStorage."); // <-- Added log
        showFeedback('Gemini API Key not found. Please set it in your profile.');
        console.error('Gemini API Key not found in localStorage.');
        return null; // Indicate failure
    }

    const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

    // --- Construct prompt with history ---
    let historyPromptPart = "";
    if (aiSessionQuestionHistory.length > 0) {
        const recentHistory = aiSessionQuestionHistory.slice(-5); // Get last 5 questions
        historyPromptPart = `\n\nAvoid generating questions similar to these previous ones:\n- ${recentHistory.join('\n- ')}`;
    }

    // Define the prompt structure for getting a JSON response
    const prompt = `Generate a multiple-choice quiz question about ${topic} suitable for someone preparing for CompTIA certifications.
Focus on cybersecurity concepts.
The question should be ${difficulty} difficulty.
${historyPromptPart}

Provide the output ONLY in the following JSON format:
{
  "question": "<The question text>",
  "options": [
    "<Option A>",
    "<Option B>",
    "<Option C>",
    "<Option D>"
  ],
  "answer": "<The correct option text>",
  "explanation": "<A brief explanation of why the answer is correct>",
  "category": "AI",
  "difficulty": "${difficulty}"
}

Ensure the JSON is valid and contains no extra text before or after the JSON block.`;
    // --- End prompt construction ---

    console.log("Generated Prompt (with history snippet):", prompt.substring(0, 500) + "..."); // Log prompt start

    const requestBody = {
        contents: [{
            parts: [{ text: prompt }]
        }],
        // Optional: Add safety settings and generation config if needed
         generationConfig: {
             responseMimeType: "application/json", // Request JSON output directly
             temperature: 2, // Adjust creativity
             maxOutputTokens: 500
         }
    };

    try {
        console.log("Constructed API request. About to call fetch..."); // <-- Added log
        const response = await fetch(API_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });
        console.log("Fetch call completed. Response status:", response.status); // <-- Added log

        if (!response.ok) {
            // Attempt to read error response body
            let errorBody = 'Could not read error body.';
            try {
                errorBody = await response.text();
            } catch (e) {}
            console.error(`API Error: ${response.status} ${response.statusText}`, errorBody);
            showFeedback(`Error generating question: ${response.statusText}. Check API key or console.`);
            return null;
        }

        const responseData = await response.json();

        // Extract the generated text (which should be our JSON string)
        if (responseData.candidates && responseData.candidates[0] && responseData.candidates[0].content && responseData.candidates[0].content.parts && responseData.candidates[0].content.parts[0]) {
            const jsonString = responseData.candidates[0].content.parts[0].text;
            console.log("Raw AI Response Text:", jsonString);

            // Parse the JSON string from the AI response
            try {
                const aiQuestion = JSON.parse(jsonString);
                console.log("Parsed AI Question:", aiQuestion);

                // Basic validation of the received question structure
                if (aiQuestion.question && Array.isArray(aiQuestion.options) && aiQuestion.options.length >= 2 && aiQuestion.answer && aiQuestion.explanation) {
                    // Add a unique ID (can be simple for now)
                    aiQuestion.id = `ai_${Date.now()}`;
                    aiQuestion.type = 'multiple_choice'; // Assuming standard multiple choice
                    // Ensure the answer is one of the options
                    if (!aiQuestion.options.includes(aiQuestion.answer)) {
                         console.warn("AI generated answer is not present in the options. Attempting to fix or discard.");
                         if (aiQuestion.options.length > 0) {
                              aiQuestion.answer = aiQuestion.options[0]; // Fallback: just pick the first option
                         } else {
                             throw new Error("AI generated question has no valid options.");
                         }
                    }

                    // --- Add to session history ---
                    aiSessionQuestionHistory.push(aiQuestion.question);
                    // Optional: Keep history trimmed (e.g., last 20 questions)
                    if (aiSessionQuestionHistory.length > 20) {
                       aiSessionQuestionHistory.shift(); // Remove the oldest
                    }
                    console.log("Current AI Session History:", aiSessionQuestionHistory);
                    // --- End add to history ---

                    return aiQuestion;
                } else {
                    console.error('AI response JSON is missing required fields:', aiQuestion);
                    showFeedback('AI response format incorrect. Try again.');
                    return null;
                }
            } catch (parseError) {
                console.error('Error parsing JSON from AI response:', parseError, "Raw text was:", jsonString);
                showFeedback('Failed to parse AI response. Check console.');
                return null;
            }
        } else {
            console.error('Unexpected API response structure:', responseData);
            showFeedback('Received unexpected response from AI. Check console.');
            return null;
        }
    } catch (error) {
        console.error('Network or fetch error generating AI question:', error);
        showFeedback('Network error generating question. Check connection or console.');
        return null;
    }
}


// Note: The call to init() is now inside the DOMContentLoaded listener at the top.


// --- AI Feature Handling ---

// Helper function to format AI explanations with proper line breaks and clickable links
function formatExplanation(text) {
    if (!text) return '<i>No explanation available.</i>';

    // First replace newlines with <br> tags for HTML display
    let formattedText = text.replace(/\n/g, '<br>');

    // Check if this is a questions.json format explanation (contains "The incorrect answers:")
    const questionsJsonFormat = formattedText.includes("The incorrect answers:");

    if (questionsJsonFormat) {
        // Format for questions.json style explanations

        // First, handle the main explanation (correct answer)
        // This could be any of A, B, C, or D followed by explanation text
        const correctAnswerMatch = formattedText.match(/^([A-D])\.\s+(.+?)(?=<br>The incorrect answers:)/is);

        if (correctAnswerMatch) {
            const correctLetter = correctAnswerMatch[1];
            const correctExplanation = correctAnswerMatch[2];

            // Wrap the correct answer explanation in a div with proper styling
            formattedText = formattedText.replace(
                /^([A-D])\.\s+(.+?)(?=<br>The incorrect answers:)/is,
                (match, letter, content) => {
                    return `<div class="main-explanation"><div class="option-${letter.toLowerCase()}"><strong>${letter}. ${content}</strong></div></div>`;
                }
            );

            // Format "The incorrect answers:" section with proper styling
            formattedText = formattedText.replace(
                /<br>The incorrect answers:/i,
                '<div class="incorrect-answers-header"><strong>The incorrect answers:</strong></div>'
            );

            // Format each incorrect answer option (A, B, C, D)
            // First, find all sections that start with A., B., C., or D. and capture their content
            const optionRegex = /<br>([A-D])\.\s+([\s\S]+?)(?=<br>[A-D]\.|<br>More information:|$)/gi;
            let match;

            // Use a while loop to process each match
            while ((match = optionRegex.exec(formattedText)) !== null) {
                const fullMatch = match[0];
                const letter = match[1];
                const content = match[2];
                const optionClass = `option-${letter.toLowerCase()}`;

                // Create replacement with proper styling - no need for option-letter span since CSS uses ::before
                const replacement = `<div class="${optionClass}">${content}</div>`;

                // Replace this specific occurrence
                formattedText = formattedText.replace(fullMatch, replacement);

                // Reset regex lastIndex to continue from where we left off
                optionRegex.lastIndex = formattedText.indexOf(replacement) + replacement.length;
            }

            // Format "More information:" section
            formattedText = formattedText.replace(
                /<br>More information:([\s\S]+?)$/is,
                '<div class="more-info-header"><strong>More information:</strong></div><div class="more-info-content">$1</div>'
            );
        }
    } else {
        // Original format for intrebari.json style explanations
        // Find the first occurrence of "Option A", "Option B", etc.
        const optionIndex = formattedText.search(/Option [A-D]/i);
        if (optionIndex > 0) {
            // Split the text into main explanation and options
            const mainExplanation = formattedText.substring(0, optionIndex);
            const optionsPart = formattedText.substring(optionIndex);

            // Wrap the main explanation in a div with a class
            formattedText = `<div class="main-explanation">${mainExplanation}</div>${optionsPart}`;
        } else {
            // If no options found, wrap the entire text
            formattedText = `<div class="main-explanation">${formattedText}</div>`;
        }
    }

    // Apply alternating colors to options A, B, C, D in the explanation
    // First pattern: Match "Option A", "Option B", etc. and their content until the next option or end of text
    formattedText = formattedText.replace(/Option A(?:\s+is\s+incorrect\.|\s+is\s+correct\.|\s+is\s+incorrect:|\s+is\s+correct:|\s*:|\.|\))?(.+?)(?=Option B|Option C|Option D|$)/gi,
        '<span class="option-a">Option A$1</span>');

    formattedText = formattedText.replace(/Option B(?:\s+is\s+incorrect\.|\s+is\s+correct\.|\s+is\s+incorrect:|\s+is\s+correct:|\s*:|\.|\))?(.+?)(?=Option A|Option C|Option D|$)/gi,
        '<span class="option-b">Option B$1</span>');

    formattedText = formattedText.replace(/Option C(?:\s+is\s+incorrect\.|\s+is\s+correct\.|\s+is\s+incorrect:|\s+is\s+correct:|\s*:|\.|\))?(.+?)(?=Option A|Option B|Option D|$)/gi,
        '<span class="option-c">Option C$1</span>');

    formattedText = formattedText.replace(/Option D(?:\s+is\s+incorrect\.|\s+is\s+correct\.|\s+is\s+incorrect:|\s+is\s+correct:|\s*:|\.|\))?(.+?)(?=Option A|Option B|Option C|$)/gi,
        '<span class="option-d">Option D$1</span>');

    // Second pattern: Match lines starting with "Option A", "Option B", etc. at the beginning of a line
    formattedText = formattedText.replace(/(^|<br>)Option A\s+([^<]+)(<br>|$)/gi,
        '$1<span class="option-a">Option A $2</span>$3');

    formattedText = formattedText.replace(/(^|<br>)Option B\s+([^<]+)(<br>|$)/gi,
        '$1<span class="option-b">Option B $2</span>$3');

    formattedText = formattedText.replace(/(^|<br>)Option C\s+([^<]+)(<br>|$)/gi,
        '$1<span class="option-c">Option C $2</span>$3');

    formattedText = formattedText.replace(/(^|<br>)Option D\s+([^<]+)(<br>|$)/gi,
        '$1<span class="option-d">Option D $2</span>$3');

    // Third pattern: Match just "Option A", "Option B" at the beginning of a line (like in your example)
    formattedText = formattedText.replace(/(^|<br>)\s*Option A\b([^<]*?)(<br>|$)/gi,
        '$1<span class="option-a">Option A$2</span>$3');

    formattedText = formattedText.replace(/(^|<br>)\s*Option B\b([^<]*?)(<br>|$)/gi,
        '$1<span class="option-b">Option B$2</span>$3');

    formattedText = formattedText.replace(/(^|<br>)\s*Option C\b([^<]*?)(<br>|$)/gi,
        '$1<span class="option-c">Option C$2</span>$3');

    formattedText = formattedText.replace(/(^|<br>)\s*Option D\b([^<]*?)(<br>|$)/gi,
        '$1<span class="option-d">Option D$2</span>$3');

    // Fourth pattern: Match just "A", "B", "C", "D" at the beginning of a line or after a break with specific formats
    formattedText = formattedText.replace(/(^|<br>)\s*A\s+(?:is\s+(?:correct|incorrect)|:|-)([^<]*?)(?=<br>|$)/gi,
        '$1<span class="option-a">A$2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*B\s+(?:is\s+(?:correct|incorrect)|:|-)([^<]*?)(?=<br>|$)/gi,
        '$1<span class="option-b">B$2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*C\s+(?:is\s+(?:correct|incorrect)|:|-)([^<]*?)(?=<br>|$)/gi,
        '$1<span class="option-c">C$2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*D\s+(?:is\s+(?:correct|incorrect)|:|-)([^<]*?)(?=<br>|$)/gi,
        '$1<span class="option-d">D$2</span>');

    // Fifth pattern: Match just "Option A", "Option B", etc. at the beginning of a paragraph (exactly like in your example)
    formattedText = formattedText.replace(/(^|<br>)\s*Option A\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-a">Option A $2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*Option B\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-b">Option B $2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*Option C\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-c">Option C $2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*Option D\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-d">Option D $2</span>');

    // Sixth pattern: Match just "A", "B", "C", "D" at the beginning of a line with any text following (exactly like in your example)
    formattedText = formattedText.replace(/(^|<br>)\s*A\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-a">A $2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*B\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-b">B $2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*C\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-c">C $2</span>');

    formattedText = formattedText.replace(/(^|<br>)\s*D\s+([^<]+?)(?=<br>|$)/gi,
        '$1<span class="option-d">D $2</span>');

    // Then convert URLs to clickable links
    // This regex matches common URL patterns
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    formattedText = formattedText.replace(urlRegex, function(url) {
        return '<a href="' + url + '" target="_blank">' + url + '</a>';
    });

    return formattedText;
}

// Special formatting function for acronym explanations
function formatAcronymExplanation(text, acronym) {
    if (!text) return '<i>No explanation available.</i>';

    // Create a structured format for the acronym explanation
    let formattedContent = '';

    // Split the text by lines to identify potential sections
    const lines = text.split(/\n/);

    // Check if the text is already formatted or needs structure
    if (lines.length <= 1 && !text.includes(':')) {
        // Simple format - just the spelled out meaning
        formattedContent = `<strong>Meaning:</strong> ${text}`;
    } else {
        // Text already has some structure, enhance it
        formattedContent = text;

        // Highlight the acronym letters if they appear at the beginning of words
        if (acronym && acronym.length > 1) {
            const letters = acronym.split('');
            const words = text.split(' ');

            // Check if this might be a letter-by-letter expansion
            let potentialExpansion = '';
            let matchCount = 0;

            for (let i = 0; i < letters.length && i < words.length; i++) {
                if (words[i].charAt(0).toUpperCase() === letters[i].toUpperCase()) {
                    matchCount++;
                    potentialExpansion += `<strong>${words[i].charAt(0)}</strong>${words[i].substring(1)} `;
                } else {
                    potentialExpansion += `${words[i]} `;
                }
            }

            // If we have a good match, use the highlighted version for the first line
            if (matchCount >= Math.min(3, letters.length)) {
                const remainingWords = words.slice(letters.length).join(' ');
                formattedContent = potentialExpansion + remainingWords;
            }
        }
    }

    // Convert markdown-style formatting to HTML

    // Convert markdown headers (## Heading) to styled headers
    formattedContent = formattedContent.replace(/##\s+(.+?)(?:\n|$)/g, '<h3 style="color:var(--primary-color);margin-top:10px;margin-bottom:5px;">$1</h3>');

    // Convert **text** to bold
    formattedContent = formattedContent.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

    // Convert *text* to italic
    formattedContent = formattedContent.replace(/\*([^*]+)\*/g, '<em>$1</em>');

    // Convert newlines to proper HTML breaks
    formattedContent = formattedContent.replace(/\n/g, '<br>');

    // Make any headers or key terms bold (format like "Term: value")
    formattedContent = formattedContent.replace(/([A-Za-z\s]+):\s/g, '<strong>$1:</strong> ');

    // Convert URLs to clickable links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    formattedContent = formattedContent.replace(urlRegex, function(url) {
        return '<a href="' + url + '" target="_blank">' + url + '</a>';
    });

    // Ensure no text is truncated by adding proper closing tags
    // This helps prevent issues with malformed HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = formattedContent;
    formattedContent = tempDiv.innerHTML;

    // Add a wrapper div with proper styling to ensure content is fully visible
    formattedContent = `<div class="explanation-content-wrapper">${formattedContent}</div>`;

    return formattedContent;
}

// Function to get deeper explanation using Gemini API (handles questions and acronyms)
async function getDeeperExplanation(itemText, contextText, itemType = 'question') { // Added itemType
    console.log(`Getting deeper explanation for ${itemType}:`, itemText);
    const apiKey = localStorage.getItem('geminiApiKey');
    if (!apiKey) {
        showFeedback('API Key not found.');
        console.error('Gemini API Key not found in localStorage.');
        return "Error: API Key missing.";
    }

    const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

    // Adjust prompt based on itemType
    let prompt;
    if (itemType === 'acronym') {
        prompt = `Provide a more detailed explanation for the following acronym and its meaning. Focus on clarifying the context or importance.

Acronym: ${itemText}

Meaning: ${contextText}

Please format your response using markdown:
- Use **bold** for important terms
- Use ## for section headers
- Use * for bullet points if needed
- Structure your response with clear sections

Detailed Explanation:`;
    } else { // Default to question
        prompt = `Provide a more detailed explanation for the following quiz question and its original explanation. Focus on clarifying the concepts involved.

Question: ${itemText}

Original Explanation: ${contextText}

Please format your response using markdown:
- Use **bold** for important terms and concepts
- Use ## for section headers
- Use * for bullet points if needed
- Structure your response with clear sections

Detailed Explanation:`;
    }


    const requestBody = {
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
             temperature: 0.7, // Adjust creativity/factuality
             maxOutputTokens: 1000 // Increased from 400 to allow for more comprehensive explanations
         }
    };

    try {
        const response = await fetch(API_ENDPOINT, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            let errorBody = await response.text();
            console.error(`API Error (Deeper Explanation): ${response.status} ${response.statusText}`, errorBody);
            showFeedback(`Error getting explanation: ${response.statusText}. Check console.`);
            return `Error: ${response.statusText}`;
        }

        const responseData = await response.json();

        if (responseData.candidates && responseData.candidates[0] && responseData.candidates[0].content && responseData.candidates[0].content.parts && responseData.candidates[0].content.parts[0]) {
            const deeperExplanation = responseData.candidates[0].content.parts[0].text;
            console.log("Received deeper explanation:", deeperExplanation);
            return deeperExplanation;
        } else {
            console.error('Unexpected API response structure (Deeper Explanation):', responseData);
            showFeedback('Received unexpected response from AI. Check console.');
            return "Error: Unexpected API response.";
        }
    } catch (error) {
        console.error('Network or fetch error (Deeper Explanation):', error);
        showFeedback('Network error getting explanation. Check connection or console.');
        return "Error: Network error.";
    }
}

// Function for AI translation using Gemini API (handles any text)
async function translateText(textToTranslate, targetLanguage = 'ro') {
    console.log(`Translating to ${targetLanguage}:`, textToTranslate);
    const apiKey = localStorage.getItem('geminiApiKey');
    if (!apiKey) {
        showFeedback('API Key not found.');
        console.error('Gemini API Key not found in localStorage.');
        return "Error: API Key missing.";
    }

    const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
    // Map language code to full name for the prompt if needed, or keep it simple
    const targetLanguageName = targetLanguage === 'ro' ? 'Romanian' : targetLanguage;
    const prompt = `Translate the following text into ${targetLanguageName}. Provide only the translation, without any introductory phrases like "Here is the translation:".

IMPORTANT: Preserve all markdown formatting in your translation, including:
- **bold text** (surrounded by double asterisks)
- ## section headers (preceded by hash symbols)
- * bullet points (preceded by asterisks)
- Any other formatting symbols

Text: """
${textToTranslate}
"""

Translation:`;

    const requestBody = {
        contents: [{ parts: [{ text: prompt }] }],
         generationConfig: {
             temperature: 0.3, // Lower temperature for more direct translation
             maxOutputTokens: 1000 // Allow longer translations if needed
         }
    };

    try {
        const response = await fetch(API_ENDPOINT, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            let errorBody = await response.text();
            console.error(`API Error (Translation): ${response.status} ${response.statusText}`, errorBody);
            showFeedback(`Error translating text: ${response.statusText}. Check console.`);
            return `Error: ${response.statusText}`;
        }

        const responseData = await response.json();

        if (responseData.candidates && responseData.candidates[0] && responseData.candidates[0].content && responseData.candidates[0].content.parts && responseData.candidates[0].content.parts[0]) {
            const translatedText = responseData.candidates[0].content.parts[0].text;
            console.log(`Received translation (${targetLanguageName}):`, translatedText);
            return translatedText;
        } else {
            console.error('Unexpected API response structure (Translation):', responseData);
            showFeedback('Received unexpected response from AI during translation. Check console.');
            return "Error: Unexpected API response.";
        }
    } catch (error) {
        console.error('Network or fetch error (Translation):', error);
        showFeedback('Network error during translation. Check connection or console.');
        return "Error: Network error.";
    }
}

// Handles clicks on the main AI Features button
function handleAiFeaturesClick() {
    console.log("AI Features button clicked. Current mode:", currentMode);

    // Show feedback to indicate the function is running
    showFeedback('Processing AI request...');

    const apiKey = localStorage.getItem('geminiApiKey');
    if (!apiKey) {
        showFeedback('Gemini API Key not found. Please set it in your profile.');
        return;
    }

    let targetContainer; // Container where AI buttons/results should go
    let aiResultDiv; // Div where results are displayed
    let itemText; // Text of the question or acronym
    let contextText; // Text of the explanation or spelled-out meaning
    let itemType; // 'question' or 'acronym'

    if (currentMode === 'acronyms') {
        console.log("Handling AI button click in acronym mode");

        targetContainer = acronymsContainer;
        itemType = 'acronym';

        // Find all revealed acronym cards
        const revealedCards = document.querySelectorAll('.acronym-card:has(.acronym-definition:not(.hidden))');
        const allCards = document.querySelectorAll('.acronym-card');

        // If no revealed cards, reveal all of them
        if (revealedCards.length === 0) {
            console.log("No revealed acronyms found, revealing all");
            allCards.forEach(card => {
                const definitionDiv = card.querySelector('.acronym-definition');
                const revealBtn = card.querySelector('.reveal-acronym-btn');
                if (definitionDiv && definitionDiv.classList.contains('hidden')) {
                    definitionDiv.classList.remove('hidden');
                    if (revealBtn) {
                        revealBtn.innerHTML = '<i class="fa-solid fa-eye-slash"></i>';
                    }
                }
            });
        }

        // Get all revealed cards (should be all of them now)
        const cardsToProcess = document.querySelectorAll('.acronym-card:has(.acronym-definition:not(.hidden))');

        if (cardsToProcess.length === 0) {
            showFeedback("No acronyms found. Please try again.");
            return;
        }

        console.log(`Found ${cardsToProcess.length} revealed acronyms to process`);

        // Collect all acronym data
        const acronymsData = [];
        cardsToProcess.forEach(card => {
            const acronymTitle = card.querySelector('.acronym-title')?.textContent || "Unknown Acronym";
            const acronymDefinition = card.querySelector('.acronym-definition')?.textContent || "No definition available";
            acronymsData.push({
                acronym: acronymTitle,
                spelledOut: acronymDefinition,
                element: card // Store reference to the card element
            });
        });

        console.log("Collected acronym data:", acronymsData);

        // Use the first acronym for the main AI request
        if (acronymsData.length > 0) {
            itemText = acronymsData[0].acronym;
            contextText = acronymsData[0].spelledOut;
        } else {
            itemText = "No acronym found";
            contextText = "No definition available";
        }

        // Store all acronyms for later use
        window.allAcronymsData = acronymsData;

        // Always remove any existing result container to ensure fresh content
        const existingResultDiv = document.getElementById('aiResultContainer');
        if (existingResultDiv) {
            existingResultDiv.remove();
        }

        // Create a new result container
        aiResultDiv = document.createElement('div');
        aiResultDiv.id = 'aiResultContainer';
        aiResultDiv.style.marginTop = '15px';
        aiResultDiv.style.padding = '10px';
        aiResultDiv.style.maxHeight = '600px';
        aiResultDiv.style.overflowY = 'auto';
        aiResultDiv.style.overflowWrap = 'break-word'; // Use overflowWrap instead of wordWrap
        aiResultDiv.style.overflowX = 'hidden';
        // Let CSS handle the styling for proper dark mode support
        aiResultDiv.className = 'ai-result-container';
        targetContainer.appendChild(aiResultDiv);

    } else { // Assume quiz mode
        targetContainer = resultContainerElement; // Target the standard result container
        itemType = 'question';
        const question = currentQuestions[currentQuestionIndex];
        if (!question) {
            console.error("Current question not found for AI features.");
            return;
        }
        itemText = question.questionText || question.question;
        contextText = question.explanation || "No base explanation available.";
        // Create container for AI results if it doesn't exist (quiz mode adds it dynamically)
        aiResultDiv = targetContainer.querySelector('#aiResultContainer');
        if (!aiResultDiv) {
            aiResultDiv = document.createElement('div');
            aiResultDiv.id = 'aiResultContainer';
            aiResultDiv.style.marginTop = '10px';
            aiResultDiv.style.padding = '10px';
            aiResultDiv.style.maxHeight = '600px';
            aiResultDiv.style.overflowY = 'auto';
            aiResultDiv.style.overflowWrap = 'break-word'; // Use overflowWrap instead of wordWrap
            aiResultDiv.style.overflowX = 'hidden';
            // Let CSS handle the styling for proper dark mode support
            aiResultDiv.className = 'ai-result-container';
            targetContainer.appendChild(aiResultDiv); // Append here
        }
    }

    if (!targetContainer) {
        console.error("Target container not found for AI features.");
        return;
    }

    // Clear previous AI results/buttons if any *within the target container*
    const existingAiButtons = targetContainer.querySelector('#aiActionButtons');
    if (existingAiButtons) existingAiButtons.remove();
    aiResultDiv.innerHTML = '<i>AI results will appear here...</i>'; // Reset result div

    // Create container for AI action buttons
    const aiButtonContainer = document.createElement('div');
    aiButtonContainer.id = 'aiActionButtons';
    aiButtonContainer.style.marginTop = '15px';
    aiButtonContainer.style.paddingTop = '10px';
    aiButtonContainer.style.borderTop = '1px solid var(--border-color)';

    // Create "Deeper Explanation" button
    const deeperExplainBtn = document.createElement('button');
    deeperExplainBtn.textContent = itemType === 'acronym' ? 'Explain All Acronyms' : 'Deeper Explanation';
    deeperExplainBtn.className = 'button button-secondary button-small';
    deeperExplainBtn.style.marginRight = '10px';
    deeperExplainBtn.onclick = async () => {
        if (itemType === 'acronym' && window.allAcronymsData && window.allAcronymsData.length > 0) {
            // Process all acronyms
            aiResultDiv.innerHTML = '<i>Generating explanations for all acronyms...</i>';
            deeperExplainBtn.disabled = true;
            translateBtn.disabled = true;

            // Create a container for all explanations
            const allExplanationsHTML = document.createElement('div');

            // Process each acronym
            for (let i = 0; i < window.allAcronymsData.length; i++) {
                const acronymData = window.allAcronymsData[i];
                const acronym = acronymData.acronym;
                const meaning = acronymData.spelledOut;

                // Add a header for this acronym
                const acronymHeader = document.createElement('h3');
                acronymHeader.textContent = acronym;
                acronymHeader.style.marginTop = i > 0 ? '20px' : '10px';
                acronymHeader.style.borderTop = i > 0 ? '1px solid var(--border-color)' : 'none';
                acronymHeader.style.paddingTop = i > 0 ? '15px' : '0';
                allExplanationsHTML.appendChild(acronymHeader);

                // Add the meaning
                const meaningPara = document.createElement('p');
                meaningPara.innerHTML = `<strong>Meaning:</strong> ${meaning}`;
                allExplanationsHTML.appendChild(meaningPara);

                // Update status
                aiResultDiv.innerHTML = `<i>Processing acronym ${i+1} of ${window.allAcronymsData.length}...</i>`;

                try {
                    // Get explanation for this acronym
                    const explanation = await getDeeperExplanation(acronym, meaning, 'acronym');

                    // Add the explanation with enhanced formatting
                    const explainDiv = document.createElement('div');
                    explainDiv.innerHTML = `<strong>Explanation:</strong><br>${formatAcronymExplanation(explanation, acronym)}`;
                    explainDiv.className = 'acronym-explanation'; // Add class for styling
                    allExplanationsHTML.appendChild(explainDiv);
                } catch (error) {
                    console.error(`Error getting explanation for ${acronym}:`, error);
                    const errorDiv = document.createElement('div');
                    errorDiv.innerHTML = `<strong>Explanation:</strong><br><i>Error generating explanation. Please try again.</i>`;
                    allExplanationsHTML.appendChild(errorDiv);
                }
            }

            // Display all explanations
            aiResultDiv.innerHTML = '';
            aiResultDiv.appendChild(allExplanationsHTML);

            deeperExplainBtn.disabled = false;
            translateBtn.disabled = false;
        } else {
            // Original behavior for single item
            aiResultDiv.innerHTML = '<i>Generating deeper explanation...</i>';
            deeperExplainBtn.disabled = true; // Disable buttons during generation
            translateBtn.disabled = true;
            // Pass itemType to the AI function
            const result = await getDeeperExplanation(itemText, contextText, itemType);
            // Use enhanced formatting for all modes
            aiResultDiv.innerHTML = formatAcronymExplanation(result, itemType === 'acronym' ? itemText : 'AI');
            // Make sure the result container is fully visible
            aiResultDiv.style.maxHeight = '600px';
            aiResultDiv.style.overflowY = 'auto';
            aiResultDiv.style.overflowWrap = 'break-word'; // Use overflowWrap instead of wordWrap
            aiResultDiv.style.overflowX = 'hidden';
            deeperExplainBtn.disabled = false;
            translateBtn.disabled = false;
        }
    };

    // Create "Translate Explanation" button
    const translateBtn = document.createElement('button');
    translateBtn.textContent = itemType === 'acronym' ? 'Translate All Meanings (RO)' : 'Translate Explanation (RO)';
    translateBtn.className = 'button button-secondary button-small';
    translateBtn.onclick = async () => {
        if (itemType === 'acronym' && window.allAcronymsData && window.allAcronymsData.length > 0) {
            // Process all acronyms for translation
            aiResultDiv.innerHTML = '<i>Generating translations for all acronyms...</i>';
            deeperExplainBtn.disabled = true;
            translateBtn.disabled = true;

            // Create a container for all translations
            const allTranslationsHTML = document.createElement('div');

            // Process each acronym
            for (let i = 0; i < window.allAcronymsData.length; i++) {
                const acronymData = window.allAcronymsData[i];
                const acronym = acronymData.acronym;
                const meaning = acronymData.spelledOut;

                // Add a header for this acronym
                const acronymHeader = document.createElement('h3');
                acronymHeader.textContent = acronym;
                acronymHeader.style.marginTop = i > 0 ? '20px' : '10px';
                acronymHeader.style.borderTop = i > 0 ? '1px solid var(--border-color)' : 'none';
                acronymHeader.style.paddingTop = i > 0 ? '15px' : '0';
                allTranslationsHTML.appendChild(acronymHeader);

                // Add the original meaning
                const originalPara = document.createElement('p');
                originalPara.innerHTML = `<strong>Original:</strong> ${meaning}`;
                allTranslationsHTML.appendChild(originalPara);

                // Update status
                aiResultDiv.innerHTML = `<i>Translating acronym ${i+1} of ${window.allAcronymsData.length}...</i>`;

                try {
                    // Get translation for this acronym
                    const translation = await translateText(meaning, 'ro');

                    // Add the translation
                    const translationDiv = document.createElement('div');
                    translationDiv.innerHTML = `<strong>Romanian:</strong><br>${formatExplanation(translation)}`;
                    allTranslationsHTML.appendChild(translationDiv);
                } catch (error) {
                    console.error(`Error translating ${acronym}:`, error);
                    const errorDiv = document.createElement('div');
                    errorDiv.innerHTML = `<strong>Romanian:</strong><br><i>Error generating translation. Please try again.</i>`;
                    allTranslationsHTML.appendChild(errorDiv);
                }
            }

            // Display all translations
            aiResultDiv.innerHTML = '';
            aiResultDiv.appendChild(allTranslationsHTML);

            deeperExplainBtn.disabled = false;
            translateBtn.disabled = false;
        } else {
            // Original behavior for single item
            // Find the text to translate (either original context or AI-generated deeper explanation)
            let textToTranslate = contextText; // Default to original context
            if (aiResultDiv && aiResultDiv.innerHTML !== '<i>AI results will appear here...</i>' && aiResultDiv.innerHTML !== '<i>Generating deeper explanation...</i>' && aiResultDiv.innerHTML !== '<i>Generating translation...</i>') {
                 // If AI result container has content (likely deeper explanation), translate that
                 textToTranslate = aiResultDiv.innerText; // Use innerText to get displayed text without HTML
            } else if (itemType === 'question') {
                 // For questions, check the original explanation box if AI result is empty
                 const explanationBox = resultContainerElement?.querySelector('#explanationBox');
                 if (explanationBox) textToTranslate = explanationBox.innerText;
            } // For acronyms, contextText (spelledOut) is the only base

            aiResultDiv.innerHTML = '<i>Generating translation...</i>';
            deeperExplainBtn.disabled = true;
            translateBtn.disabled = true;
            const result = await translateText(textToTranslate, 'ro');
            // Use enhanced formatting for all modes
            aiResultDiv.innerHTML = formatAcronymExplanation(result, itemType === 'acronym' ? itemText : 'Translation');
            // Make sure the result container is fully visible
            aiResultDiv.style.maxHeight = '600px';
            aiResultDiv.style.overflowY = 'auto';
            aiResultDiv.style.overflowWrap = 'break-word'; // Use overflowWrap instead of wordWrap
            aiResultDiv.style.overflowX = 'hidden';
            deeperExplainBtn.disabled = false;
            translateBtn.disabled = false;
        }
    };

    aiButtonContainer.appendChild(deeperExplainBtn);
    aiButtonContainer.appendChild(translateBtn);

    // First ensure aiResultDiv is a child of targetContainer
    if (!targetContainer.contains(aiResultDiv)) {
        targetContainer.appendChild(aiResultDiv);
    }

    // Now we can safely insert the button container before the result div
    targetContainer.insertBefore(aiButtonContainer, aiResultDiv);
    // Note: aiResultDiv is already appended in quiz mode or exists in acronym mode
}
